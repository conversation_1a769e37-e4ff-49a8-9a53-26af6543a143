# QuizFlow

QuizFlow is a standardized, flexible, and interactive quiz ecosystem for creating, sharing, and taking quizzes. It defines and implements a comprehensive JSON-based file format for quizzes, named QuizFlow JSON (QFJSON).

## Features

- **Standardized Format**: Create quizzes using a well-documented, extensible, and universally understandable JSON format.
- **Rich Interactivity**: Support for multiple question types, media integration, and dynamic content.
- **User Authentication**: Secure user accounts with email/password and OAuth providers.
- **Quiz Management**: Create, edit, and publish quizzes with various question types.
- **Responsive Design**: Works on desktop and mobile devices.

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API Routes
- **Database**: MongoDB with Prisma ORM
- **Authentication**: NextAuth.js

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/quizflow.git
   cd quizflow
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start MongoDB with Docker:
   ```bash
   docker-compose up -d
   ```

4. Initialize MongoDB replica set:
   ```bash
   docker exec quizflow-mongodb mongosh --eval "rs.initiate({_id: 'rs0', members: [{_id: 0, host: 'localhost:27017'}]})"
   ```

5. Set up the database:
   ```bash
   npm run db:push
   ```

6. Seed the database with hacking quizzes:
   ```bash
   npm run seed
   ```

7. Run the development server:
   ```bash
   npm run dev
   ```

8. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Seeded Hacking Quizzes

The application comes pre-loaded with 6 comprehensive hacking-related quizzes:

1. **Ethical Hacking Fundamentals** - Basic principles and methodologies
2. **Web Application Security** - OWASP Top 10, SQL injection, XSS, and web vulnerabilities
3. **Network Security & Penetration Testing** - Network scanning, reconnaissance, and attack techniques
4. **Social Engineering & Physical Security** - Human psychology, phishing, and physical security assessment
5. **Cryptography & Digital Forensics** - Hash functions, encryption, and forensics principles
6. **Malware Analysis & Reverse Engineering** - Static/dynamic analysis and reverse engineering techniques

Each quiz follows the QFJSON specification and includes multiple question types:
- Multiple choice (single and multiple correct answers)
- True/False
- Short answer
- Fill-in-the-blank
- Matching
- Essay questions

### Admin Access

#### Default Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: admin

#### Admin Setup
To create or reset the admin user:
```bash
npm run setup-admin
```

#### Admin Features
- **Admin Dashboard**: Access via `/dashboard/admin` (visible only to admin users)
- **User Management**: View and manage all users
- **Quiz Management**: Manage all quizzes across the platform
- **System Analytics**: View comprehensive platform statistics
- **Admin Navigation**: Special admin menu item in the dashboard navbar

## QuizFlow JSON (QFJSON) Format

QuizFlow uses a standardized JSON format for quizzes. The format supports:

- Multiple question types (multiple choice, true/false, short answer, matching, fill-in-the-blank, essay)
- Rich media integration (images, audio, video)
- Multilingual content
- Dynamic question selection from pools
- Detailed scoring rules

## Project Structure

```
quizflow/
├── prisma/              # Prisma schema and migrations
├── public/              # Static assets
├── src/
│   ├── app/             # Next.js app router
│   │   ├── api/         # API routes
│   │   ├── auth/        # Authentication pages
│   │   ├── dashboard/   # Dashboard pages
│   │   └── ...
│   ├── components/      # React components
│   │   ├── auth/        # Authentication components
│   │   ├── dashboard/   # Dashboard components
│   │   ├── quiz/        # Quiz components
│   │   └── ui/          # UI components
│   ├── lib/             # Utility functions
│   └── types/           # TypeScript type definitions
└── ...
```
