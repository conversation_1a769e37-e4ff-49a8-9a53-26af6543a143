/**
 * QuizFlow JSON Parser
 *
 * This module provides utilities for parsing and validating QuizFlow JSON (QFJSON) files.
 */

import { z } from 'zod';
import {
  QuizFlowJSON,
  Question,
  QuestionPool,
  SelectionRule,
  TextContent
} from '@/types/qfjson';

// Helper function to get text content based on locale
export function getLocalizedText(text: TextContent | undefined | null, locale?: string): string {
  // Handle null/undefined cases
  if (!text) {
    return '';
  }

  if (typeof text === 'string') {
    return text;
  }

  // Ensure text is an object before accessing properties
  if (typeof text !== 'object') {
    return String(text);
  }

  // If locale is provided, try to get that specific translation
  if (locale && text[locale]) {
    return text[locale];
  }

  // Fall back to default
  return text.default || '';
}

// Basic validation schema for QFJSON
const mediaItemSchema = z.object({
  type: z.enum(['image', 'audio', 'video']),
  url: z.string().url(),
  alt_text: z.string().optional(),
  caption: z.string().optional(),
});

const hintItemSchema = z.object({
  text: z.string(),
  delay_seconds: z.number().optional(),
});

const dependencySchema = z.object({
  question_id: z.string(),
  condition: z.enum([
    'correct',
    'incorrect',
    'answered',
    'not_answered',
    'answer_is',
    'selected_option_id'
  ]),
  value: z.union([z.string(), z.array(z.string())]).optional(),
});

const baseQuestionSchema = z.object({
  question_id: z.string(),
  type: z.string(),
  text: z.union([z.string(), z.record(z.string())]),
  points: z.number().positive(),
  feedback_correct: z.string().optional(),
  feedback_incorrect: z.string().optional(),
  explanation: z.union([z.string(), z.record(z.string())]).optional(),
  media: z.array(mediaItemSchema).optional(),
  hint: z.array(hintItemSchema).optional(),
  depends_on: dependencySchema.optional(),
});

const metadataSchema = z.object({
  format_version: z.string(),
  quiz_id: z.string(),
  title: z.union([z.string(), z.record(z.string())]),
  description: z.union([z.string(), z.record(z.string())]).optional(),
  author: z.string().optional(),
  creation_date: z.string().optional(), // ISO 8601 format
  tags: z.array(z.string()).optional(),
  passing_score_percentage: z.number().min(0).max(100).optional(),
  time_limit_minutes: z.number().positive().optional(),
  markup_format: z.enum(['markdown', 'html', 'plain_text']).optional(),
  locale: z.string().optional(),
});

const quizFlowSchema = z.object({
  quiz: z.object({
    $schema: z.string().optional(),
    metadata: metadataSchema,
    questions: z.array(baseQuestionSchema).optional(),
    question_pools: z.array(
      z.object({
        pool_id: z.string(),
        title: z.string().optional(),
        description: z.string().optional(),
        questions: z.array(baseQuestionSchema),
      })
    ).optional(),
    selection_rules: z.array(
      z.object({
        pool_id: z.string(),
        select_count: z.number().positive(),
        randomize: z.boolean().optional(),
        shuffle_order: z.boolean().optional(),
      })
    ).optional(),
  }),
});

/**
 * Validates a QuizFlow JSON object against the basic schema
 * @param data The QuizFlow JSON object to validate
 * @returns A tuple with [isValid, errors]
 */
export function validateQuizFlowJSON(data: any): [boolean, any] {
  try {
    quizFlowSchema.parse(data);
    return [true, null];
  } catch (error) {
    return [false, error];
  }
}

/**
 * Parses a QuizFlow JSON string into a strongly-typed object
 * @param jsonString The QuizFlow JSON string to parse
 * @returns The parsed QuizFlow JSON object
 */
export function parseQuizFlowJSON(jsonString: string): QuizFlowJSON {
  try {
    const data = JSON.parse(jsonString);
    const [isValid, errors] = validateQuizFlowJSON(data);

    if (!isValid) {
      throw new Error(`Invalid QuizFlow JSON: ${JSON.stringify(errors)}`);
    }

    return data as QuizFlowJSON;
  } catch (error) {
    throw new Error(`Failed to parse QuizFlow JSON: ${error}`);
  }
}

/**
 * Assembles the final list of questions for a quiz based on question pools and selection rules
 * @param quiz The QuizFlow JSON object
 * @returns An array of questions to be presented in the quiz
 */
export function assembleQuizQuestions(quiz: QuizFlowJSON): Question[] {
  // If there are no question pools or selection rules, return the questions array directly
  if (!quiz.quiz.question_pools || !quiz.quiz.selection_rules || quiz.quiz.question_pools.length === 0) {
    return quiz.quiz.questions || [];
  }

  const selectedQuestions: Question[] = [];

  // Process each selection rule
  quiz.quiz.selection_rules.forEach((rule: SelectionRule) => {
    // Find the corresponding question pool
    const pool = quiz.quiz.question_pools?.find(p => p.pool_id === rule.pool_id);

    if (pool && pool.questions.length > 0) {
      let poolQuestions = [...pool.questions];

      // Randomize if specified
      if (rule.randomize) {
        poolQuestions = shuffleArray(poolQuestions);
      }

      // Select the specified number of questions (or all if select_count is greater)
      const count = Math.min(rule.select_count, poolQuestions.length);
      const selected = poolQuestions.slice(0, count);

      // Shuffle the order of selected questions if specified
      if (rule.shuffle_order) {
        shuffleArray(selected);
      }

      selectedQuestions.push(...selected);
    }
  });

  // Add any questions directly in the quiz.questions array
  if (quiz.quiz.questions && quiz.quiz.questions.length > 0) {
    selectedQuestions.push(...quiz.quiz.questions);
  }

  return selectedQuestions;
}

// Helper function to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}
