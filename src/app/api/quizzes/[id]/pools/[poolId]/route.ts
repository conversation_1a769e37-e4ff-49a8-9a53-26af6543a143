import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// Get a specific pool
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string; poolId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: quizId, poolId } = await params;

    const pool = await db.questionPool.findUnique({
      where: {
        id: poolId,
        quizId: quizId,
      },
      include: {
        questions: true,
      },
    });

    if (!pool) {
      return NextResponse.json(
        { message: "Pool not found" },
        { status: 404 }
      );
    }

    // Verify access
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found" },
        { status: 404 }
      );
    }

    // If quiz is not published, only the creator can view it
    if (!quiz.isPublished && (!session || quiz.creatorId !== session.user.id)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    return NextResponse.json(pool);
  } catch (error) {
    console.error("Error fetching pool:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching the pool" },
      { status: 500 }
    );
  }
}

// Update a pool
export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string; poolId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Forbidden: Only administrators can modify quizzes" },
        { status: 403 }
      );
    }

    const { id: quizId, poolId } = await params;
    const body = await req.json();

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Verify pool exists and belongs to the quiz
    const pool = await db.questionPool.findUnique({
      where: {
        id: poolId,
        quizId: quizId,
      },
    });

    if (!pool) {
      return NextResponse.json(
        { message: "Pool not found" },
        { status: 404 }
      );
    }

    // Update the pool
    const updatedPool = await db.questionPool.update({
      where: {
        id: poolId,
      },
      data: body,
    });

    return NextResponse.json(updatedPool);
  } catch (error) {
    console.error("Error updating pool:", error);
    return NextResponse.json(
      { message: "An error occurred while updating the pool" },
      { status: 500 }
    );
  }
}

// Delete a pool
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ id: string; poolId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Forbidden: Only administrators can modify quizzes" },
        { status: 403 }
      );
    }

    const { id: quizId, poolId } = await params;

    // Verify ownership
    const quiz = await db.quiz.findUnique({
      where: {
        id: quizId,
        creatorId: session.user.id,
      },
    });

    if (!quiz) {
      return NextResponse.json(
        { message: "Quiz not found or you don't have permission to edit it" },
        { status: 404 }
      );
    }

    // Verify pool exists and belongs to the quiz
    const pool = await db.questionPool.findUnique({
      where: {
        id: poolId,
        quizId: quizId,
      },
    });

    if (!pool) {
      return NextResponse.json(
        { message: "Pool not found" },
        { status: 404 }
      );
    }

    // Delete any selection rules that use this pool
    await db.selectionRule.deleteMany({
      where: {
        quizId: quizId,
        poolId: pool.poolId,
      },
    });

    // Delete the pool
    await db.questionPool.delete({
      where: {
        id: poolId,
      },
    });

    return NextResponse.json(
      { message: "Pool deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting pool:", error);
    return NextResponse.json(
      { message: "An error occurred while deleting the pool" },
      { status: 500 }
    );
  }
}
