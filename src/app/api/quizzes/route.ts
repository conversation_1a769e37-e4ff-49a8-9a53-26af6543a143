import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (session.user.role !== "admin") {
      return NextResponse.json(
        { message: "Forbidden: Only administrators can create quizzes" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { quizId, title, description, tags, passingScore, timeLimit } = body;

    if (!quizId || !title) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    // Create the quiz
    const quiz = await db.quiz.create({
      data: {
        quizId,
        title,
        description,
        tags,
        passingScore,
        timeLimit,
        creatorId: session.user.id,
      },
    });

    return NextResponse.json(quiz, { status: 201 });
  } catch (error) {
    console.error("Error creating quiz:", error);
    return NextResponse.json(
      { message: "An error occurred while creating the quiz" },
      { status: 500 }
    );
  }
}

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    // If requesting user's quizzes, ensure they're authenticated
    if (userId && (!session || session.user.id !== userId)) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const whereClause = userId
      ? { creatorId: userId }
      : { isPublished: true };

    const quizzes = await db.quiz.findMany({
      where: whereClause,
      orderBy: {
        updatedAt: "desc",
      },
    });

    return NextResponse.json(quizzes);
  } catch (error) {
    console.error("Error fetching quizzes:", error);
    return NextResponse.json(
      { message: "An error occurred while fetching quizzes" },
      { status: 500 }
    );
  }
}
