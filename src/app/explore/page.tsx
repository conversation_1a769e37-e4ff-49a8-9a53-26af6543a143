import Link from "next/link";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";

export default async function ExplorePage() {
  const session = await getServerSession(authOptions);

  // Get published quizzes
  const quizzes = await db.quiz.findMany({
    where: {
      isPublished: true,
    },
    include: {
      creator: {
        select: {
          name: true,
        },
      },
      _count: {
        select: {
          questions: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
    take: 12,
  });

  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="border-b">
        <div className="container mx-auto px-4 flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link href="/" className="text-xl font-bold">
              QuizFlow
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link
                href="/features"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Features
              </Link>
              <Link
                href="/explore"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Explore
              </Link>
              <Link
                href="/docs"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Documentation
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            {session ? (
              <Button asChild>
                <Link href="/dashboard">Dashboard</Link>
              </Button>
            ) : (
              <div className="flex items-center gap-4">
                <Button variant="outline" asChild>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button asChild>
                  <Link href="/auth/register">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="flex-1">
        <section className="py-12 md:py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-12">
              <h1 className="text-3xl md:text-4xl font-bold mb-4">Explore Quizzes</h1>
              <p className="text-lg text-muted-foreground">
                Discover and take quizzes created by the QuizFlow community
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4 mb-8 justify-center">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search quizzes..."
                  className="w-full md:w-80 p-2 pl-10 border rounded-md"
                />
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <select className="p-2 border rounded-md">
                <option value="">All Categories</option>
                <option value="security">Security</option>
                <option value="programming">Programming</option>
                <option value="networking">Networking</option>
                <option value="other">Other</option>
              </select>
            </div>

            {quizzes.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium mb-2">No quizzes available</h3>
                <p className="text-muted-foreground mb-6">
                  There are no published quizzes available at the moment.
                </p>
                {session && session.user.role === "admin" && (
                  <Button asChild>
                    <Link href="/dashboard/quizzes/create">Create a Quiz</Link>
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {quizzes.map((quiz: any) => (
                  <Card key={quiz.id} className="h-full flex flex-col">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="line-clamp-1">{quiz.title}</CardTitle>
                          <CardDescription>
                            By {quiz.creator?.name || "Anonymous"}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <p className="text-sm text-muted-foreground line-clamp-2 mb-4">
                        {quiz.description || "No description provided"}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {quiz.tags.map((tag: any, index: number) => (
                          <span
                            key={index}
                            className="inline-block px-2 py-1 text-xs bg-muted rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <p className="text-muted-foreground">Questions</p>
                          <p className="font-medium">{quiz._count?.questions || 0}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Time Limit</p>
                          <p className="font-medium">
                            {quiz.timeLimit ? `${quiz.timeLimit} min` : "None"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="border-t pt-4">
                      <div className="w-full flex justify-between items-center">
                        <div className="text-xs text-muted-foreground">
                          Updated {formatDate(quiz.updatedAt)}
                        </div>
                        <Button asChild>
                          <Link href={`/quiz/${quiz.id}`}>Take Quiz</Link>
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 border-t">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            &copy; {new Date().getFullYear()} QuizFlow. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
