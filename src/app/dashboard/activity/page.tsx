import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default async function ActivityPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  // Get user's activity data
  const [
    userQuizzes,
    userResponses,
    recentResponses
  ] = await Promise.all([
    // User's created quizzes
    db.quiz.findMany({
      where: { creatorId: session.user.id },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        _count: {
          select: {
            questions: true,
            responses: true,
          }
        }
      }
    }),
    // User's quiz responses
    db.userResponse.findMany({
      where: { userId: session.user.id },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        quiz: {
          select: {
            title: true,
            passingScore: true,
          }
        }
      }
    }),
    // Recent responses to user's quizzes (if user has created quizzes)
    db.userResponse.findMany({
      where: {
        quiz: {
          creatorId: session.user.id
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        user: {
          select: {
            name: true,
            email: true,
          }
        },
        quiz: {
          select: {
            title: true,
          }
        }
      }
    })
  ]);

  const formatScore = (score: number, totalPoints: number) => {
    if (totalPoints === 0) return '0%';
    return `${Math.round((score / totalPoints) * 100)}%`;
  };

  const getScoreBadgeVariant = (score: number, totalPoints: number, passingScore: number) => {
    const percentage = totalPoints > 0 ? (score / totalPoints) * 100 : 0;
    return percentage >= passingScore ? 'default' : 'secondary';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Activity Dashboard</h1>
        <p className="text-muted-foreground">Track your quiz creation and participation activity</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quizzes Created</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
              <polyline points="14,2 14,8 20,8" />
              <line x1="16" y1="13" x2="8" y2="13" />
              <line x1="16" y1="17" x2="8" y2="17" />
              <polyline points="10,9 9,9 8,9" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userQuizzes.length}</div>
            <p className="text-xs text-muted-foreground">
              Total questions: {userQuizzes.reduce((sum, quiz) => sum + quiz._count.questions, 0)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quizzes Taken</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <polyline points="22 12 18 12 15 21 9 3 6 12 2 12" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userResponses.length}</div>
            <p className="text-xs text-muted-foreground">
              Avg Score: {userResponses.length > 0
                ? Math.round(userResponses.reduce((sum, response) =>
                    sum + (response.totalPoints > 0 ? (response.score / response.totalPoints) * 100 : 0), 0
                  ) / userResponses.length)
                : 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Responses</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {userQuizzes.reduce((sum, quiz) => sum + quiz._count.responses, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              To your quizzes
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* My Quiz Results */}
        <Card>
          <CardHeader>
            <CardTitle>My Quiz Results</CardTitle>
            <CardDescription>Your recent quiz attempts</CardDescription>
          </CardHeader>
          <CardContent>
            {userResponses.length > 0 ? (
              <div className="space-y-4">
                {userResponses.map((response) => (
                  <div key={response.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{response.quiz.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(response.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant={getScoreBadgeVariant(response.score, response.totalPoints, response.quiz.passingScore)}>
                        {formatScore(response.score, response.totalPoints)}
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {response.score}/{response.totalPoints} points
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">You haven't taken any quizzes yet</p>
                <Button asChild>
                  <Link href="/explore">Explore Quizzes</Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* My Quizzes */}
        <Card>
          <CardHeader>
            <CardTitle>My Quizzes</CardTitle>
            <CardDescription>Quizzes you've created</CardDescription>
          </CardHeader>
          <CardContent>
            {userQuizzes.length > 0 ? (
              <div className="space-y-4">
                {userQuizzes.map((quiz) => (
                  <div key={quiz.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{quiz.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {quiz._count.questions} questions • {quiz._count.responses} responses
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/quiz/${quiz.id}`}>View</Link>
                      </Button>
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/dashboard/quizzes/${quiz.id}`}>Edit</Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  {session.user.role === "admin"
                    ? "You haven't created any quizzes yet"
                    : "No quiz creation activity"}
                </p>
                {session.user.role === "admin" && (
                  <Button asChild>
                    <Link href="/dashboard/quizzes/create">Create Quiz</Link>
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Responses to My Quizzes */}
      {recentResponses.length > 0 && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Recent Activity on My Quizzes</CardTitle>
            <CardDescription>Latest responses to your quizzes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentResponses.map((response) => (
                <div key={response.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{response.user.name}</p>
                    <p className="text-sm text-muted-foreground">
                      took "{response.quiz.title}" • {new Date(response.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {formatScore(response.score, response.totalPoints)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {response.score}/{response.totalPoints} points
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
