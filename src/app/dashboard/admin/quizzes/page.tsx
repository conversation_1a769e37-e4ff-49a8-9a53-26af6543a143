import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default async function AdminQuizzesPage() {
  const session = await getServerSession(authOptions);

  if (!session || session.user.role !== 'admin') {
    redirect('/dashboard');
  }

  // Get all quizzes with creator and response counts
  const quizzes = await db.quiz.findMany({
    orderBy: { createdAt: 'desc' },
    include: {
      creator: {
        select: {
          name: true,
          email: true,
        }
      },
      _count: {
        select: {
          questions: true,
          responses: true,
        }
      }
    }
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Quiz Management</h1>
            <p className="text-muted-foreground">Manage all quizzes in the system</p>
          </div>
          <Button asChild>
            <Link href="/dashboard/admin">← Back to Admin</Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Quizzes ({quizzes.length})</CardTitle>
          <CardDescription>Complete list of all quizzes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {quizzes.map((quiz) => (
              <div key={quiz.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div>
                      <p className="font-medium">{quiz.title}</p>
                      <p className="text-sm text-muted-foreground">
                        by {quiz.creator.name} ({quiz.creator.email})
                      </p>
                    </div>
                    <Badge variant={quiz.isPublished ? 'default' : 'secondary'}>
                      {quiz.isPublished ? 'Published' : 'Draft'}
                    </Badge>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {quiz.description}
                    </p>
                  </div>
                  <div className="mt-2 flex gap-4 text-sm text-muted-foreground">
                    <span>Created: {new Date(quiz.createdAt).toLocaleDateString()}</span>
                    <span>Questions: {quiz._count.questions}</span>
                    <span>Responses: {quiz._count.responses}</span>
                    <span>Time Limit: {quiz.timeLimit} min</span>
                    <span>Passing Score: {quiz.passingScore}%</span>
                  </div>
                  <div className="mt-2 flex gap-2">
                    {quiz.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/quiz/${quiz.id}`}>View Quiz</Link>
                  </Button>
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/dashboard/quizzes/${quiz.id}`}>Edit</Link>
                  </Button>
                  <Button variant="outline" size="sm">
                    Analytics
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
