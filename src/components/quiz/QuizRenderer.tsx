"use client";

import React, { useState, useEffect, useCallback } from "react";
import { QuizFlowJSON, Question } from "@/types/qfjson";
import { assembleQuizQuestions, getLocalizedText } from "@/lib/utils/qfjson-parser";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import QuestionRenderer from "./QuestionRenderer";
import { calculatePercentage } from "@/lib/utils";

interface QuizRendererProps {
  quiz: QuizFlowJSON;
  onComplete?: (score: number, answers: Record<string, any>) => void;
}

const QuizRenderer: React.FC<QuizRendererProps> = ({ quiz, onComplete }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [score, setScore] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);

  // Initialize quiz
  useEffect(() => {
    const assembledQuestions = assembleQuizQuestions(quiz);
    setQuestions(assembledQuestions);

    // Initialize time limit if specified
    if (quiz.quiz.metadata.time_limit_minutes) {
      setTimeRemaining(quiz.quiz.metadata.time_limit_minutes * 60); // Convert to seconds
    }
  }, [quiz]);

  const handleSubmitQuiz = useCallback(() => {
    // Calculate score
    let totalScore = 0;

    questions.forEach(question => {
      // This is a simplified scoring logic - in a real implementation,
      // you would need more complex logic based on question type
      const answer = answers[question.question_id];
      if (answer) {
        // For true/false questions
        if (question.type === "true_false" && answer === (question as any).correct_answer) {
          totalScore += question.points;
        }
        // For multiple choice with single answer
        else if (question.type === "multiple_choice" &&
                (question as any).single_correct_answer === true) {
          const correctOption = (question as any).options.find((opt: any) => opt.is_correct);
          if (correctOption && answer === correctOption.id) {
            totalScore += question.points;
          }
        }
        // For short answer questions
        else if (question.type === "short_answer") {
          const correctAnswers = (question as any).correct_answers;
          if (correctAnswers.includes(answer)) {
            totalScore += question.points;
          }
        }
        // Other question types would need their own scoring logic
      }
    });

    setScore(totalScore);
    setIsCompleted(true);

    if (onComplete) {
      onComplete(totalScore, answers);
    }
  }, [questions, answers, onComplete]);

  // Timer effect
  useEffect(() => {
    if (timeRemaining === null || timeRemaining <= 0 || isCompleted) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev === null || prev <= 1) {
          clearInterval(timer);
          if (prev === 1) {
            handleSubmitQuiz();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining, isCompleted, handleSubmitQuiz]);

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };



  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (questions.length === 0) {
    return <div>Loading quiz...</div>;
  }

  const currentQuestion = questions[currentQuestionIndex];
  const metadata = quiz.quiz.metadata;
  const locale = metadata.locale || "en-US";

  if (isCompleted) {
    const percentage = calculatePercentage(score, questions.reduce((sum, q) => sum + q.points, 0));
    const isPassing = metadata.passing_score_percentage
      ? percentage >= metadata.passing_score_percentage
      : true;

    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Quiz Results</CardTitle>
          <CardDescription>
            {getLocalizedText(metadata.title, locale)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="text-2xl font-bold">
                {isPassing ? "Congratulations!" : "Quiz Completed"}
              </h3>
              <p className="text-xl mt-2">
                Your score: {score} / {questions.reduce((sum, q) => sum + q.points, 0)} ({percentage}%)
              </p>
              {metadata.passing_score_percentage && (
                <p className="mt-2">
                  {isPassing
                    ? "You passed the quiz!"
                    : `You need ${metadata.passing_score_percentage}% to pass the quiz.`}
                </p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="justify-center">
          <Button onClick={() => window.location.reload()}>
            Take Another Quiz
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{getLocalizedText(metadata.title, locale)}</CardTitle>
            <CardDescription>
              Question {currentQuestionIndex + 1} of {questions.length}
            </CardDescription>
          </div>
          {timeRemaining !== null && (
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Time Remaining</p>
              <p className="text-xl font-semibold">{formatTime(timeRemaining)}</p>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <QuestionRenderer
          question={currentQuestion}
          answer={answers[currentQuestion.question_id]}
          onAnswerChange={(answer) => handleAnswerChange(currentQuestion.question_id, answer)}
          locale={locale}
          showExplanation={isCompleted}
        />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePreviousQuestion}
          disabled={currentQuestionIndex === 0}
        >
          Previous
        </Button>
        <div className="flex gap-2">
          {currentQuestionIndex < questions.length - 1 ? (
            <Button onClick={handleNextQuestion}>Next</Button>
          ) : (
            <Button onClick={handleSubmitQuiz}>Submit Quiz</Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default QuizRenderer;
