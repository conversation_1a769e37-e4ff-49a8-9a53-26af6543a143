"use client";

import React, { useState, useEffect } from "react";
import { EssayQuestion as EssayQuestionType } from "@/types/qfjson";

interface EssayQuestionProps {
  question: EssayQuestionType;
  answer: string | undefined | null;
  onAnswerChange: (answer: string) => void;
  disabled?: boolean;
}

const EssayQuestion: React.FC<EssayQuestionProps> = ({
  question,
  answer = "",
  onAnswerChange,
  disabled = false,
}) => {
  const [wordCount, setWordCount] = useState(0);

  useEffect(() => {
    // Count words in the answer
    const answerText = typeof answer === 'string' ? answer : '';
    const trimmedAnswer = answerText.trim();
    const words = trimmedAnswer.split(/\s+/);
    setWordCount(trimmedAnswer === "" ? 0 : words.length);
  }, [answer]);

  const isUnderMinWordCount =
    question.min_word_count !== undefined &&
    wordCount < question.min_word_count;

  const isOverMaxWordCount =
    question.max_word_count !== undefined &&
    wordCount > question.max_word_count;

  return (
    <div className="space-y-4">
      {question.guidelines && (
        <div className="p-4 bg-muted rounded-md">
          <h4 className="font-medium mb-2">Guidelines:</h4>
          <div className="text-sm">{question.guidelines}</div>
        </div>
      )}

      <div className="relative">
        <textarea
          value={typeof answer === 'string' ? answer : ''}
          onChange={(e) => onAnswerChange(e.target.value)}
          disabled={disabled}
          placeholder="Type your answer here..."
          rows={8}
          className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed"
        />
      </div>

      <div className="flex justify-between text-sm">
        <div>
          {(question.min_word_count !== undefined || question.max_word_count !== undefined) && (
            <span className={
              isUnderMinWordCount || isOverMaxWordCount
                ? "text-destructive"
                : "text-muted-foreground"
            }>
              Word count: {wordCount}
              {question.min_word_count !== undefined && ` (min: ${question.min_word_count})`}
              {question.max_word_count !== undefined && ` (max: ${question.max_word_count})`}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default EssayQuestion;
