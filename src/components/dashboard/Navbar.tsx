"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut, useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";

export default function Navbar() {
  const pathname = usePathname();
  const { data: session } = useSession();

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`);
  };

  return (
    <header className="border-b">
      <div className="container mx-auto px-4 flex h-16 items-center justify-between">
        <div className="flex items-center gap-6">
          <Link href="/" className="text-xl font-bold">
            QuizFlow
          </Link>
          <nav className="hidden md:flex gap-6">
            <Link
              href="/dashboard"
              className={`text-sm font-medium transition-colors hover:text-primary ${
                isActive("/dashboard") && !isActive("/dashboard/quizzes") && !isActive("/dashboard/activity")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              Dashboard
            </Link>
            <Link
              href="/dashboard/quizzes"
              className={`text-sm font-medium transition-colors hover:text-primary ${
                isActive("/dashboard/quizzes")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              My Quizzes
            </Link>
            <Link
              href="/dashboard/activity"
              className={`text-sm font-medium transition-colors hover:text-primary ${
                isActive("/dashboard/activity")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              Activity
            </Link>
            <Link
              href="/explore"
              className={`text-sm font-medium transition-colors hover:text-primary ${
                isActive("/explore")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              Explore
            </Link>
            {session?.user?.role === 'admin' && (
              <Link
                href="/dashboard/admin"
                className={`text-sm font-medium transition-colors hover:text-primary ${
                  isActive("/dashboard/admin")
                    ? "text-primary"
                    : "text-muted-foreground"
                }`}
              >
                Admin
              </Link>
            )}
          </nav>
        </div>
        <div className="flex items-center gap-4">
          {session ? (
            <div className="flex items-center gap-4">
              <div className="hidden md:block">
                <div className="text-sm font-medium">{session.user.name}</div>
                <div className="text-xs text-muted-foreground">{session.user.email}</div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => signOut({ callbackUrl: "/" })}
              >
                Sign Out
              </Button>
            </div>
          ) : (
            <Button asChild size="sm">
              <Link href="/auth/login">Sign In</Link>
            </Button>
          )}
        </div>
      </div>
    </header>
  );
}
