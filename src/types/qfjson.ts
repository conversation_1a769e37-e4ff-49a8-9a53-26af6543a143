/**
 * TypeScript definitions for QuizFlow JSON (QFJSON) format
 */

// Common types
export type LanguageMap = {
  default: string;
  [languageTag: string]: string; // e.g., "en-US", "fr-FR"
};

export type TextContent = string | LanguageMap;

export type MediaItem = {
  type: "image" | "audio" | "video";
  url: string;
  alt_text?: string;
  caption?: string;
};

export type HintItem = {
  text: string;
  delay_seconds?: number;
};

export type DependencyCondition =
  | "correct"
  | "incorrect"
  | "answered"
  | "not_answered"
  | "answer_is"
  | "selected_option_id";

export type Dependency = {
  question_id: string;
  condition: DependencyCondition;
  value?: string | string[]; // For "answer_is" and "selected_option_id" conditions
};

// Question types
export type BaseQuestion = {
  question_id: string;
  type: string;
  text: TextContent;
  points: number;
  feedback_correct?: string;
  feedback_incorrect?: string;
  explanation?: TextContent; // General explanation shown after answering
  media?: MediaItem[];
  hint?: HintItem[];
  depends_on?: Dependency;
};

export type MultipleChoiceOption = {
  id: string;
  text: TextContent;
  is_correct: boolean;
  feedback?: string;
  explanation?: TextContent; // Detailed explanation for this option
  points_if_selected?: number;
};

export type MultipleChoiceQuestion = BaseQuestion & {
  type: "multiple_choice";
  single_correct_answer: boolean;
  options: MultipleChoiceOption[];
  scoring_method?: "all_or_nothing" | "partial_credit";
};

export type TrueFalseQuestion = BaseQuestion & {
  type: "true_false";
  correct_answer: boolean;
};

export type ShortAnswerQuestion = BaseQuestion & {
  type: "short_answer";
  correct_answers: string[];
  case_sensitive?: boolean;
  trim_whitespace?: boolean;
  exact_match?: boolean;
};

export type MatchingStem = {
  id: string;
  text: TextContent;
};

export type MatchingOption = {
  id: string;
  text: TextContent;
};

export type MatchingPair = {
  stem_id: string;
  option_id: string;
  points_for_pair?: number;
};

export type MatchingQuestion = BaseQuestion & {
  type: "matching";
  stems: MatchingStem[];
  options: MatchingOption[];
  correct_pairs: MatchingPair[];
};

export type BlankDefinition = {
  id: string;
  correct_answers: string[];
  case_sensitive?: boolean;
  trim_whitespace?: boolean;
  hint?: string;
};

export type FillInTheBlankQuestion = BaseQuestion & {
  type: "fill_in_the_blank";
  text_template: TextContent;
  blanks: BlankDefinition[];
};

export type EssayQuestion = BaseQuestion & {
  type: "essay";
  min_word_count?: number;
  max_word_count?: number;
  guidelines?: string;
};

export type Question =
  | MultipleChoiceQuestion
  | TrueFalseQuestion
  | ShortAnswerQuestion
  | MatchingQuestion
  | FillInTheBlankQuestion
  | EssayQuestion;

// Question Pool and Selection Rules
export type QuestionPool = {
  pool_id: string;
  title?: string;
  description?: string;
  questions: Question[];
};

export type SelectionRule = {
  pool_id: string;
  select_count: number;
  randomize?: boolean;
  shuffle_order?: boolean;
};

// Quiz Metadata
export type QuizMetadata = {
  format_version: string;
  quiz_id: string;
  title: TextContent;
  description?: TextContent;
  author?: string;
  creation_date?: string; // ISO 8601 format
  tags?: string[];
  passing_score_percentage?: number;
  time_limit_minutes?: number;
  markup_format?: "markdown" | "html" | "plain_text";
  locale?: string; // BCP 47 language tag
};

// Complete Quiz Structure
export type QuizFlowJSON = {
  quiz: {
    $schema?: string;
    metadata: QuizMetadata;
    questions?: Question[];
    question_pools?: QuestionPool[];
    selection_rules?: SelectionRule[];
  };
};
