// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// Quiz model
model Quiz {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  quizId          String    @unique // The quiz_id from the QFJSON format
  title           String
  description     String?
  author          String?
  creationDate    DateTime  @default(now())
  tags            String[]
  passingScore    Float?
  timeLimit       Int?      // in minutes
  markupFormat    String    @default("markdown")
  locale          String    @default("en-US")
  formatVersion   String    @default("1.1")
  isPublished     Boolean   @default(false)

  // Relations
  questions       Question[]
  questionPools   QuestionPool[]
  selectionRules  SelectionRule[]
  responses       UserResponse[]
  creator         User?     @relation("CreatedBy", fields: [creatorId], references: [id])
  creatorId       String?   @db.ObjectId

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Question model
model Question {
  id                String    @id @default(auto()) @map("_id") @db.ObjectId
  questionId        String    // The question_id from the QFJSON format
  type              String    // multiple_choice, true_false, short_answer, etc.
  text              Json      // Can be a string or an object for multilingual support
  points            Float
  feedbackCorrect   String?
  feedbackIncorrect String?
  explanation       Json?     // General explanation shown after answering (can be string or multilingual object)
  media             Json?     // Array of media objects
  hint              Json?     // Array of hint objects
  dependsOn         Json?     // Dependency object

  // Type-specific fields stored as JSON
  options           Json?     // For multiple_choice
  correctAnswer     Json?     // For true_false, short_answer
  correctAnswers    Json?     // For short_answer
  caseSensitive     Boolean?  // For short_answer
  trimWhitespace    Boolean?  // For short_answer
  exactMatch        Boolean?  // For short_answer
  stems             Json?     // For matching
  correctPairs      Json?     // For matching
  textTemplate      Json?     // For fill_in_the_blank
  blanks            Json?     // For fill_in_the_blank
  minWordCount      Int?      // For essay
  maxWordCount      Int?      // For essay
  guidelines        String?   // For essay

  // Relations
  quiz              Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId            String    @db.ObjectId
  questionPool      QuestionPool? @relation(fields: [questionPoolId], references: [id])
  questionPoolId    String?   @db.ObjectId

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

// QuestionPool model
model QuestionPool {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  poolId      String    // The pool_id from the QFJSON format
  title       String?
  description String?
  questions   Question[]

  // Relations
  quiz        Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId      String    @db.ObjectId

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// SelectionRule model
model SelectionRule {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  poolId      String    // References the pool_id in QuestionPool
  selectCount Int
  randomize   Boolean   @default(false)
  shuffleOrder Boolean  @default(false)

  // Relations
  quiz        Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId      String    @db.ObjectId

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// User model for authentication
model User {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  name            String?
  email           String    @unique
  emailVerified   DateTime?
  password        String?
  image           String?
  role            String    @default("user") // "user", "admin", "instructor"
  createdQuizzes  Quiz[]    @relation("CreatedBy")
  responses       UserResponse[]
  accounts        Account[]
  sessions        Session[]
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Account model for OAuth providers
model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.String
  access_token      String? @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.String
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// Session model for managing user sessions
model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// UserResponse model to track quiz attempts
model UserResponse {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  user        User?     @relation(fields: [userId], references: [id])
  userId      String?   @db.ObjectId
  quiz        Quiz      @relation(fields: [quizId], references: [id])
  quizId      String    @db.ObjectId
  answers     Json      // JSON object with question IDs as keys and user answers as values
  score       Float
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  timeSpent   Int?      // in seconds

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}
