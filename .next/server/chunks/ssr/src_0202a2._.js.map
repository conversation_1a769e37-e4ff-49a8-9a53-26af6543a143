{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,iKAAc,IAAI;AAE/B,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,iKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuizDetailsForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Quiz } from \"@/generated/prisma\";\nimport { Button } from \"@/components/ui/button\";\n\ninterface QuizDetailsFormProps {\n  quiz: Quiz;\n  onSave: (updatedDetails: Partial<Quiz>) => void;\n  isSaving: boolean;\n}\n\nexport default function QuizDetailsForm({ quiz, onSave, isSaving }: QuizDetailsFormProps) {\n  const [title, setTitle] = useState(quiz.title);\n  const [description, setDescription] = useState(quiz.description || \"\");\n  const [tags, setTags] = useState(quiz.tags.join(\", \"));\n  const [passingScore, setPassingScore] = useState(quiz.passingScore?.toString() || \"70\");\n  const [timeLimit, setTimeLimit] = useState(quiz.timeLimit?.toString() || \"15\");\n  const [locale, setLocale] = useState(quiz.locale);\n  const [markupFormat, setMarkupFormat] = useState(quiz.markupFormat);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    const updatedDetails: Partial<Quiz> = {\n      title,\n      description: description || null,\n      tags: tags.split(\",\").map(tag => tag.trim()).filter(tag => tag),\n      passingScore: passingScore ? parseFloat(passingScore) : null,\n      timeLimit: timeLimit ? parseInt(timeLimit) : null,\n      locale,\n      markupFormat,\n    };\n    \n    onSave(updatedDetails);\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"space-y-2\">\n        <label htmlFor=\"title\" className=\"text-sm font-medium\">\n          Quiz Title\n        </label>\n        <input\n          id=\"title\"\n          type=\"text\"\n          value={title}\n          onChange={(e) => setTitle(e.target.value)}\n          className=\"w-full p-2 border rounded-md\"\n          required\n        />\n      </div>\n      \n      <div className=\"space-y-2\">\n        <label htmlFor=\"description\" className=\"text-sm font-medium\">\n          Description\n        </label>\n        <textarea\n          id=\"description\"\n          value={description}\n          onChange={(e) => setDescription(e.target.value)}\n          className=\"w-full p-2 border rounded-md min-h-[100px]\"\n          placeholder=\"Enter quiz description\"\n        />\n      </div>\n      \n      <div className=\"space-y-2\">\n        <label htmlFor=\"tags\" className=\"text-sm font-medium\">\n          Tags\n        </label>\n        <input\n          id=\"tags\"\n          type=\"text\"\n          value={tags}\n          onChange={(e) => setTags(e.target.value)}\n          className=\"w-full p-2 border rounded-md\"\n          placeholder=\"Enter tags separated by commas (e.g., security, basics, networking)\"\n        />\n        <p className=\"text-xs text-muted-foreground\">\n          Separate tags with commas (e.g., security, basics, networking)\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div className=\"space-y-2\">\n          <label htmlFor=\"passingScore\" className=\"text-sm font-medium\">\n            Passing Score (%)\n          </label>\n          <input\n            id=\"passingScore\"\n            type=\"number\"\n            min=\"0\"\n            max=\"100\"\n            value={passingScore}\n            onChange={(e) => setPassingScore(e.target.value)}\n            className=\"w-full p-2 border rounded-md\"\n          />\n        </div>\n        \n        <div className=\"space-y-2\">\n          <label htmlFor=\"timeLimit\" className=\"text-sm font-medium\">\n            Time Limit (minutes)\n          </label>\n          <input\n            id=\"timeLimit\"\n            type=\"number\"\n            min=\"1\"\n            value={timeLimit}\n            onChange={(e) => setTimeLimit(e.target.value)}\n            className=\"w-full p-2 border rounded-md\"\n          />\n        </div>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div className=\"space-y-2\">\n          <label htmlFor=\"locale\" className=\"text-sm font-medium\">\n            Locale\n          </label>\n          <select\n            id=\"locale\"\n            value={locale}\n            onChange={(e) => setLocale(e.target.value)}\n            className=\"w-full p-2 border rounded-md\"\n          >\n            <option value=\"en-US\">English (US)</option>\n            <option value=\"en-GB\">English (UK)</option>\n            <option value=\"es-ES\">Spanish</option>\n            <option value=\"fr-FR\">French</option>\n            <option value=\"de-DE\">German</option>\n            <option value=\"ja-JP\">Japanese</option>\n            <option value=\"zh-CN\">Chinese (Simplified)</option>\n          </select>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <label htmlFor=\"markupFormat\" className=\"text-sm font-medium\">\n            Markup Format\n          </label>\n          <select\n            id=\"markupFormat\"\n            value={markupFormat}\n            onChange={(e) => setMarkupFormat(e.target.value)}\n            className=\"w-full p-2 border rounded-md\"\n          >\n            <option value=\"markdown\">Markdown</option>\n            <option value=\"html\">HTML</option>\n            <option value=\"plain_text\">Plain Text</option>\n          </select>\n        </div>\n      </div>\n      \n      <div className=\"flex justify-end pt-4\">\n        <Button type=\"submit\" disabled={isSaving}>\n          {isSaving ? \"Saving...\" : \"Save Details\"}\n        </Button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAYe,SAAS,gBAAgB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAwB;IACtF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,WAAW,IAAI;IACnE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,YAAY,EAAE,cAAc;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,SAAS,EAAE,cAAc;IACzE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,MAAM;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,YAAY;IAElE,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,MAAM,iBAAgC;YACpC;YACA,aAAa,eAAe;YAC5B,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO;YAC3D,cAAc,eAAe,WAAW,gBAAgB;YACxD,WAAW,YAAY,SAAS,aAAa;YAC7C;YACA;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAQ,WAAU;kCAAsB;;;;;;kCAGvD,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAU;wBACV,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAAsB;;;;;;kCAG7D,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAO,WAAU;kCAAsB;;;;;;kCAGtD,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wBACvC,WAAU;wBACV,aAAY;;;;;;kCAEd,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAsB;;;;;;0CAG9D,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAY,WAAU;0CAAsB;;;;;;0CAG3D,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAAsB;;;;;;0CAGxD,8OAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAsB;;;;;;0CAG9D,8OAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAS,UAAU;8BAC7B,WAAW,cAAc;;;;;;;;;;;;;;;;;AAKpC"}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/question-forms/MultipleChoiceForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { generateUUID } from \"@/lib/utils\";\n\ninterface MultipleChoiceFormProps {\n  data: any;\n  onChange: (data: any) => void;\n}\n\nexport default function MultipleChoiceForm({ data, onChange }: MultipleChoiceFormProps) {\n  const [singleCorrectAnswer, setSingleCorrectAnswer] = useState(\n    data?.single_correct_answer !== undefined ? data.single_correct_answer : true\n  );\n  \n  const [options, setOptions] = useState<Array<{\n    id: string;\n    text: string;\n    is_correct: boolean;\n    feedback?: string;\n  }>>(\n    data?.options || [\n      { id: generateUUID(), text: \"\", is_correct: false },\n      { id: generateUUID(), text: \"\", is_correct: false },\n    ]\n  );\n  \n  const [scoringMethod, setScoringMethod] = useState(\n    data?.scoring_method || \"all_or_nothing\"\n  );\n\n  // Update parent component when form data changes\n  useEffect(() => {\n    onChange({\n      single_correct_answer: singleCorrectAnswer,\n      options,\n      scoring_method: scoringMethod,\n    });\n  }, [singleCorrectAnswer, options, scoringMethod, onChange]);\n\n  const handleOptionTextChange = (id: string, text: string) => {\n    setOptions(\n      options.map((option) =>\n        option.id === id ? { ...option, text } : option\n      )\n    );\n  };\n\n  const handleOptionCorrectChange = (id: string, isCorrect: boolean) => {\n    if (singleCorrectAnswer && isCorrect) {\n      // For single-choice questions, only one option can be correct\n      setOptions(\n        options.map((option) => ({\n          ...option,\n          is_correct: option.id === id,\n        }))\n      );\n    } else {\n      // For multiple-choice questions, multiple options can be correct\n      setOptions(\n        options.map((option) =>\n          option.id === id ? { ...option, is_correct: isCorrect } : option\n        )\n      );\n    }\n  };\n\n  const handleOptionFeedbackChange = (id: string, feedback: string) => {\n    setOptions(\n      options.map((option) =>\n        option.id === id ? { ...option, feedback } : option\n      )\n    );\n  };\n\n  const addOption = () => {\n    setOptions([\n      ...options,\n      { id: generateUUID(), text: \"\", is_correct: false },\n    ]);\n  };\n\n  const removeOption = (id: string) => {\n    if (options.length <= 2) {\n      alert(\"A multiple choice question must have at least 2 options.\");\n      return;\n    }\n    setOptions(options.filter((option) => option.id !== id));\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-2\">\n        <label className=\"text-sm font-medium\">Question Type</label>\n        <div className=\"flex space-x-4\">\n          <label className=\"flex items-center space-x-2\">\n            <input\n              type=\"radio\"\n              checked={singleCorrectAnswer}\n              onChange={() => setSingleCorrectAnswer(true)}\n            />\n            <span>Single Choice (Radio Buttons)</span>\n          </label>\n          <label className=\"flex items-center space-x-2\">\n            <input\n              type=\"radio\"\n              checked={!singleCorrectAnswer}\n              onChange={() => setSingleCorrectAnswer(false)}\n            />\n            <span>Multiple Choice (Checkboxes)</span>\n          </label>\n        </div>\n      </div>\n\n      {!singleCorrectAnswer && (\n        <div className=\"space-y-2\">\n          <label className=\"text-sm font-medium\">Scoring Method</label>\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center space-x-2\">\n              <input\n                type=\"radio\"\n                checked={scoringMethod === \"all_or_nothing\"}\n                onChange={() => setScoringMethod(\"all_or_nothing\")}\n              />\n              <span>All or Nothing</span>\n            </label>\n            <label className=\"flex items-center space-x-2\">\n              <input\n                type=\"radio\"\n                checked={scoringMethod === \"partial_credit\"}\n                onChange={() => setScoringMethod(\"partial_credit\")}\n              />\n              <span>Partial Credit</span>\n            </label>\n          </div>\n          <p className=\"text-xs text-muted-foreground\">\n            All or Nothing: Full points only if all correct options are selected and no incorrect options.\n            <br />\n            Partial Credit: Points awarded based on correct selections.\n          </p>\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        <label className=\"text-sm font-medium\">Options</label>\n        {options.map((option, index) => (\n          <div key={option.id} className=\"border rounded-md p-4 space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-medium\">Option {index + 1}</h4>\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => removeOption(option.id)}\n                className=\"h-8 w-8 p-0\"\n              >\n                <span className=\"sr-only\">Remove</span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-4 w-4\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                  />\n                </svg>\n              </Button>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Option Text</label>\n              <input\n                type=\"text\"\n                value={option.text}\n                onChange={(e) => handleOptionTextChange(option.id, e.target.value)}\n                className=\"w-full p-2 border rounded-md\"\n                placeholder=\"Enter option text\"\n                required\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type={singleCorrectAnswer ? \"radio\" : \"checkbox\"}\n                  checked={option.is_correct}\n                  onChange={(e) => handleOptionCorrectChange(option.id, e.target.checked)}\n                  name=\"correctOption\"\n                />\n                <span>Correct Answer</span>\n              </label>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Feedback for this option</label>\n              <input\n                type=\"text\"\n                value={option.feedback || \"\"}\n                onChange={(e) => handleOptionFeedbackChange(option.id, e.target.value)}\n                className=\"w-full p-2 border rounded-md\"\n                placeholder=\"Optional feedback when this option is selected\"\n              />\n            </div>\n          </div>\n        ))}\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={addOption}\n          className=\"w-full\"\n        >\n          Add Option\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,mBAAmB,EAAE,IAAI,EAAE,QAAQ,EAA2B;IACpF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3D,MAAM,0BAA0B,YAAY,KAAK,qBAAqB,GAAG;IAG3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAMnC,MAAM,WAAW;QACf;YAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;YAAK,MAAM;YAAI,YAAY;QAAM;QAClD;YAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;YAAK,MAAM;YAAI,YAAY;QAAM;KACnD;IAGH,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,MAAM,kBAAkB;IAG1B,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP,uBAAuB;YACvB;YACA,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAqB;QAAS;QAAe;KAAS;IAE1D,MAAM,yBAAyB,CAAC,IAAY;QAC1C,WACE,QAAQ,GAAG,CAAC,CAAC,SACX,OAAO,EAAE,KAAK,KAAK;gBAAE,GAAG,MAAM;gBAAE;YAAK,IAAI;IAG/C;IAEA,MAAM,4BAA4B,CAAC,IAAY;QAC7C,IAAI,uBAAuB,WAAW;YACpC,8DAA8D;YAC9D,WACE,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;oBACvB,GAAG,MAAM;oBACT,YAAY,OAAO,EAAE,KAAK;gBAC5B,CAAC;QAEL,OAAO;YACL,iEAAiE;YACjE,WACE,QAAQ,GAAG,CAAC,CAAC,SACX,OAAO,EAAE,KAAK,KAAK;oBAAE,GAAG,MAAM;oBAAE,YAAY;gBAAU,IAAI;QAGhE;IACF;IAEA,MAAM,6BAA6B,CAAC,IAAY;QAC9C,WACE,QAAQ,GAAG,CAAC,CAAC,SACX,OAAO,EAAE,KAAK,KAAK;gBAAE,GAAG,MAAM;gBAAE;YAAS,IAAI;IAGnD;IAEA,MAAM,YAAY;QAChB,WAAW;eACN;YACH;gBAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;gBAAK,MAAM;gBAAI,YAAY;YAAM;SACnD;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,MAAM,IAAI,GAAG;YACvB,MAAM;YACN;QACF;QACA,WAAW,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,IAAM,uBAAuB;;;;;;kDAEzC,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS,CAAC;wCACV,UAAU,IAAM,uBAAuB;;;;;;kDAEzC,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAKX,CAAC,qCACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS,kBAAkB;wCAC3B,UAAU,IAAM,iBAAiB;;;;;;kDAEnC,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS,kBAAkB;wCAC3B,UAAU,IAAM,iBAAiB;;;;;;kDAEnC,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAGV,8OAAC;wBAAE,WAAU;;4BAAgC;0CAE3C,8OAAC;;;;;4BAAK;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;oBACtC,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4BAAoB,WAAU;;8CAC7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAc;gDAAQ,QAAQ;;;;;;;sDAC5C,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,aAAa,OAAO,EAAE;4CACrC,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDACC,OAAM;oDACN,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAKV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CACC,MAAK;4CACL,OAAO,OAAO,IAAI;4CAClB,UAAU,CAAC,IAAM,uBAAuB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4CACjE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAM,sBAAsB,UAAU;gDACtC,SAAS,OAAO,UAAU;gDAC1B,UAAU,CAAC,IAAM,0BAA0B,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,OAAO;gDACtE,MAAK;;;;;;0DAEP,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CACC,MAAK;4CACL,OAAO,OAAO,QAAQ,IAAI;4CAC1B,UAAU,CAAC,IAAM,2BAA2B,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4CACrE,WAAU;4CACV,aAAY;;;;;;;;;;;;;2BAxDR,OAAO,EAAE;;;;;kCA6DrB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT"}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/question-forms/TrueFalseForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\n\ninterface TrueFalseFormProps {\n  data: any;\n  onChange: (data: any) => void;\n}\n\nexport default function TrueFalseForm({ data, onChange }: TrueFalseFormProps) {\n  const [correctAnswer, setCorrectAnswer] = useState(\n    data?.correct_answer !== undefined ? data.correct_answer : true\n  );\n\n  // Update parent component when form data changes\n  useEffect(() => {\n    onChange({\n      correct_answer: correctAnswer,\n    });\n  }, [correctAnswer, onChange]);\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"space-y-2\">\n        <label className=\"text-sm font-medium\">Correct Answer</label>\n        <div className=\"flex space-x-4\">\n          <label className=\"flex items-center space-x-2\">\n            <input\n              type=\"radio\"\n              checked={correctAnswer === true}\n              onChange={() => setCorrectAnswer(true)}\n              name=\"correctAnswer\"\n            />\n            <span>True</span>\n          </label>\n          <label className=\"flex items-center space-x-2\">\n            <input\n              type=\"radio\"\n              checked={correctAnswer === false}\n              onChange={() => setCorrectAnswer(false)}\n              name=\"correctAnswer\"\n            />\n            <span>False</span>\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAsB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,MAAM,mBAAmB,YAAY,KAAK,cAAc,GAAG;IAG7D,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,WAAU;8BAAsB;;;;;;8BACvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,kBAAkB;oCAC3B,UAAU,IAAM,iBAAiB;oCACjC,MAAK;;;;;;8CAEP,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,kBAAkB;oCAC3B,UAAU,IAAM,iBAAiB;oCACjC,MAAK;;;;;;8CAEP,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB"}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/question-forms/ShortAnswerForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\n\ninterface ShortAnswerFormProps {\n  data: any;\n  onChange: (data: any) => void;\n}\n\nexport default function ShortAnswerForm({ data, onChange }: ShortAnswerFormProps) {\n  const [correctAnswers, setCorrectAnswers] = useState<string[]>(\n    data?.correct_answers || [\"\"]\n  );\n  const [caseSensitive, setCaseSensitive] = useState(\n    data?.case_sensitive !== undefined ? data.case_sensitive : false\n  );\n  const [trimWhitespace, setTrimWhitespace] = useState(\n    data?.trim_whitespace !== undefined ? data.trim_whitespace : true\n  );\n  const [exactMatch, setExactMatch] = useState(\n    data?.exact_match !== undefined ? data.exact_match : true\n  );\n\n  // Update parent component when form data changes\n  useEffect(() => {\n    onChange({\n      correct_answers: correctAnswers.filter(answer => answer.trim() !== \"\"),\n      case_sensitive: caseSensitive,\n      trim_whitespace: trimWhitespace,\n      exact_match: exactMatch,\n    });\n  }, [correctAnswers, caseSensitive, trimWhitespace, exactMatch, onChange]);\n\n  const handleAnswerChange = (index: number, value: string) => {\n    const newAnswers = [...correctAnswers];\n    newAnswers[index] = value;\n    setCorrectAnswers(newAnswers);\n  };\n\n  const addAnswer = () => {\n    setCorrectAnswers([...correctAnswers, \"\"]);\n  };\n\n  const removeAnswer = (index: number) => {\n    if (correctAnswers.length <= 1) {\n      alert(\"You must have at least one correct answer.\");\n      return;\n    }\n    const newAnswers = [...correctAnswers];\n    newAnswers.splice(index, 1);\n    setCorrectAnswers(newAnswers);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <label className=\"text-sm font-medium\">Correct Answers</label>\n        <p className=\"text-xs text-muted-foreground\">\n          Add multiple acceptable answers. The question will be marked correct if the student's answer matches any of these.\n        </p>\n        \n        {correctAnswers.map((answer, index) => (\n          <div key={index} className=\"flex items-center space-x-2\">\n            <input\n              type=\"text\"\n              value={answer}\n              onChange={(e) => handleAnswerChange(index, e.target.value)}\n              className=\"flex-1 p-2 border rounded-md\"\n              placeholder=\"Enter a correct answer\"\n              required\n            />\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => removeAnswer(index)}\n              className=\"h-8 w-8 p-0\"\n              disabled={correctAnswers.length <= 1}\n            >\n              <span className=\"sr-only\">Remove</span>\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-4 w-4\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                />\n              </svg>\n            </Button>\n          </div>\n        ))}\n        \n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={addAnswer}\n          className=\"w-full\"\n        >\n          Add Another Correct Answer\n        </Button>\n      </div>\n\n      <div className=\"space-y-2\">\n        <label className=\"text-sm font-medium\">Answer Options</label>\n        <div className=\"space-y-2\">\n          <label className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              checked={caseSensitive}\n              onChange={(e) => setCaseSensitive(e.target.checked)}\n            />\n            <span>Case Sensitive</span>\n          </label>\n          <p className=\"text-xs text-muted-foreground ml-6\">\n            If checked, \"Answer\" and \"answer\" will be treated as different answers.\n          </p>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <label className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              checked={trimWhitespace}\n              onChange={(e) => setTrimWhitespace(e.target.checked)}\n            />\n            <span>Trim Whitespace</span>\n          </label>\n          <p className=\"text-xs text-muted-foreground ml-6\">\n            If checked, leading and trailing spaces will be ignored.\n          </p>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <label className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              checked={exactMatch}\n              onChange={(e) => setExactMatch(e.target.checked)}\n            />\n            <span>Exact Match Required</span>\n          </label>\n          <p className=\"text-xs text-muted-foreground ml-6\">\n            If unchecked, partial matches or keyword detection may be used (implementation dependent).\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAwB;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACjD,MAAM,mBAAmB;QAAC;KAAG;IAE/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,MAAM,mBAAmB,YAAY,KAAK,cAAc,GAAG;IAE7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACjD,MAAM,oBAAoB,YAAY,KAAK,eAAe,GAAG;IAE/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzC,MAAM,gBAAgB,YAAY,KAAK,WAAW,GAAG;IAGvD,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP,iBAAiB,eAAe,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,OAAO;YACnE,gBAAgB;YAChB,iBAAiB;YACjB,aAAa;QACf;IACF,GAAG;QAAC;QAAgB;QAAe;QAAgB;QAAY;KAAS;IAExE,MAAM,qBAAqB,CAAC,OAAe;QACzC,MAAM,aAAa;eAAI;SAAe;QACtC,UAAU,CAAC,MAAM,GAAG;QACpB,kBAAkB;IACpB;IAEA,MAAM,YAAY;QAChB,kBAAkB;eAAI;YAAgB;SAAG;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,eAAe,MAAM,IAAI,GAAG;YAC9B,MAAM;YACN;QACF;QACA,MAAM,aAAa;eAAI;SAAe;QACtC,WAAW,MAAM,CAAC,OAAO;QACzB,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;oBAI5C,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,UAAU,eAAe,MAAM,IAAI;;sDAEnC,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CACC,OAAM;4CACN,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;2BA7BA;;;;;kCAoCZ,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;;;;;;kDAEpD,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,OAAO;;;;;;kDAErD,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;;;;;;kDAEjD,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;;;AAO5D"}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/question-forms/MatchingForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { generateUUID } from \"@/lib/utils\";\n\ninterface MatchingFormProps {\n  data: any;\n  onChange: (data: any) => void;\n}\n\nexport default function MatchingForm({ data, onChange }: MatchingFormProps) {\n  const [stems, setStems] = useState<Array<{\n    id: string;\n    text: string;\n  }>>(\n    data?.stems || [\n      { id: generateUUID(), text: \"\" },\n      { id: generateUUID(), text: \"\" },\n    ]\n  );\n  \n  const [options, setOptions] = useState<Array<{\n    id: string;\n    text: string;\n  }>>(\n    data?.options || [\n      { id: generateUUID(), text: \"\" },\n      { id: generateUUID(), text: \"\" },\n    ]\n  );\n  \n  const [correctPairs, setCorrectPairs] = useState<Array<{\n    stem_id: string;\n    option_id: string;\n    points_for_pair?: number;\n  }>>(\n    data?.correct_pairs || []\n  );\n\n  // Initialize correct pairs if they don't exist\n  useEffect(() => {\n    if (correctPairs.length === 0 && stems.length > 0 && options.length > 0) {\n      const initialPairs = stems.map((stem) => ({\n        stem_id: stem.id,\n        option_id: \"\",\n      }));\n      setCorrectPairs(initialPairs);\n    }\n  }, [stems, options, correctPairs.length]);\n\n  // Update parent component when form data changes\n  useEffect(() => {\n    onChange({\n      stems,\n      options,\n      correct_pairs: correctPairs.filter(pair => pair.stem_id && pair.option_id),\n    });\n  }, [stems, options, correctPairs, onChange]);\n\n  const handleStemTextChange = (id: string, text: string) => {\n    setStems(\n      stems.map((stem) =>\n        stem.id === id ? { ...stem, text } : stem\n      )\n    );\n  };\n\n  const handleOptionTextChange = (id: string, text: string) => {\n    setOptions(\n      options.map((option) =>\n        option.id === id ? { ...option, text } : option\n      )\n    );\n  };\n\n  const handlePairChange = (stemId: string, optionId: string) => {\n    setCorrectPairs(\n      correctPairs.map((pair) =>\n        pair.stem_id === stemId ? { ...pair, option_id: optionId } : pair\n      )\n    );\n  };\n\n  const addStem = () => {\n    const newStemId = generateUUID();\n    setStems([...stems, { id: newStemId, text: \"\" }]);\n    setCorrectPairs([...correctPairs, { stem_id: newStemId, option_id: \"\" }]);\n  };\n\n  const removeStem = (id: string) => {\n    if (stems.length <= 2) {\n      alert(\"A matching question must have at least 2 stems.\");\n      return;\n    }\n    setStems(stems.filter((stem) => stem.id !== id));\n    setCorrectPairs(correctPairs.filter((pair) => pair.stem_id !== id));\n  };\n\n  const addOption = () => {\n    setOptions([...options, { id: generateUUID(), text: \"\" }]);\n  };\n\n  const removeOption = (id: string) => {\n    if (options.length <= 2) {\n      alert(\"A matching question must have at least 2 options.\");\n      return;\n    }\n    setOptions(options.filter((option) => option.id !== id));\n    setCorrectPairs(\n      correctPairs.map((pair) =>\n        pair.option_id === id ? { ...pair, option_id: \"\" } : pair\n      )\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <label className=\"text-sm font-medium\">Stems (Left Side)</label>\n        {stems.map((stem, index) => (\n          <div key={stem.id} className=\"flex items-center space-x-2\">\n            <input\n              type=\"text\"\n              value={stem.text}\n              onChange={(e) => handleStemTextChange(stem.id, e.target.value)}\n              className=\"flex-1 p-2 border rounded-md\"\n              placeholder={`Stem ${index + 1}`}\n              required\n            />\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => removeStem(stem.id)}\n              className=\"h-8 w-8 p-0\"\n              disabled={stems.length <= 2}\n            >\n              <span className=\"sr-only\">Remove</span>\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-4 w-4\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                />\n              </svg>\n            </Button>\n          </div>\n        ))}\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={addStem}\n          className=\"w-full\"\n        >\n          Add Stem\n        </Button>\n      </div>\n\n      <div className=\"space-y-4\">\n        <label className=\"text-sm font-medium\">Options (Right Side)</label>\n        {options.map((option, index) => (\n          <div key={option.id} className=\"flex items-center space-x-2\">\n            <input\n              type=\"text\"\n              value={option.text}\n              onChange={(e) => handleOptionTextChange(option.id, e.target.value)}\n              className=\"flex-1 p-2 border rounded-md\"\n              placeholder={`Option ${index + 1}`}\n              required\n            />\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => removeOption(option.id)}\n              className=\"h-8 w-8 p-0\"\n              disabled={options.length <= 2}\n            >\n              <span className=\"sr-only\">Remove</span>\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-4 w-4\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                />\n              </svg>\n            </Button>\n          </div>\n        ))}\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={addOption}\n          className=\"w-full\"\n        >\n          Add Option\n        </Button>\n      </div>\n\n      <div className=\"space-y-4\">\n        <label className=\"text-sm font-medium\">Correct Matches</label>\n        <p className=\"text-xs text-muted-foreground\">\n          For each stem, select the matching option.\n        </p>\n        \n        {stems.map((stem, index) => {\n          const pair = correctPairs.find((p) => p.stem_id === stem.id);\n          \n          return (\n            <div key={stem.id} className=\"flex items-center space-x-2\">\n              <div className=\"flex-1 p-2 border rounded-md bg-muted\">\n                {stem.text || `Stem ${index + 1}`}\n              </div>\n              <div className=\"text-center px-2\">matches</div>\n              <select\n                value={pair?.option_id || \"\"}\n                onChange={(e) => handlePairChange(stem.id, e.target.value)}\n                className=\"flex-1 p-2 border rounded-md\"\n                required\n              >\n                <option value=\"\">-- Select matching option --</option>\n                {options.map((option) => (\n                  <option key={option.id} value={option.id}>\n                    {option.text || `Option ${options.findIndex((o) => o.id === option.id) + 1}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAqB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAI/B,MAAM,SAAS;QACb;YAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;YAAK,MAAM;QAAG;QAC/B;YAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;YAAK,MAAM;QAAG;KAChC;IAGH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAInC,MAAM,WAAW;QACf;YAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;YAAK,MAAM;QAAG;QAC/B;YAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;YAAK,MAAM;QAAG;KAChC;IAGH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAK7C,MAAM,iBAAiB,EAAE;IAG3B,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,KAAK,QAAQ,MAAM,GAAG,GAAG;YACvE,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;oBACxC,SAAS,KAAK,EAAE;oBAChB,WAAW;gBACb,CAAC;YACD,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAO;QAAS,aAAa,MAAM;KAAC;IAExC,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP;YACA;YACA,eAAe,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,IAAI,KAAK,SAAS;QAC3E;IACF,GAAG;QAAC;QAAO;QAAS;QAAc;KAAS;IAE3C,MAAM,uBAAuB,CAAC,IAAY;QACxC,SACE,MAAM,GAAG,CAAC,CAAC,OACT,KAAK,EAAE,KAAK,KAAK;gBAAE,GAAG,IAAI;gBAAE;YAAK,IAAI;IAG3C;IAEA,MAAM,yBAAyB,CAAC,IAAY;QAC1C,WACE,QAAQ,GAAG,CAAC,CAAC,SACX,OAAO,EAAE,KAAK,KAAK;gBAAE,GAAG,MAAM;gBAAE;YAAK,IAAI;IAG/C;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,gBACE,aAAa,GAAG,CAAC,CAAC,OAChB,KAAK,OAAO,KAAK,SAAS;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAS,IAAI;IAGnE;IAEA,MAAM,UAAU;QACd,MAAM,YAAY,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;QAC7B,SAAS;eAAI;YAAO;gBAAE,IAAI;gBAAW,MAAM;YAAG;SAAE;QAChD,gBAAgB;eAAI;YAAc;gBAAE,SAAS;gBAAW,WAAW;YAAG;SAAE;IAC1E;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,MAAM,MAAM,IAAI,GAAG;YACrB,MAAM;YACN;QACF;QACA,SAAS,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;QAC5C,gBAAgB,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,OAAO,KAAK;IACjE;IAEA,MAAM,YAAY;QAChB,WAAW;eAAI;YAAS;gBAAE,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;gBAAK,MAAM;YAAG;SAAE;IAC3D;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,MAAM,IAAI,GAAG;YACvB,MAAM;YACN;QACF;QACA,WAAW,QAAQ,MAAM,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK;QACpD,gBACE,aAAa,GAAG,CAAC,CAAC,OAChB,KAAK,SAAS,KAAK,KAAK;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAG,IAAI;IAG3D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;oBACtC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAkB,WAAU;;8CAC3B,8OAAC;oCACC,MAAK;oCACL,OAAO,KAAK,IAAI;oCAChB,UAAU,CAAC,IAAM,qBAAqB,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oCAC7D,WAAU;oCACV,aAAa,CAAC,KAAK,EAAE,QAAQ,GAAG;oCAChC,QAAQ;;;;;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,WAAW,KAAK,EAAE;oCACjC,WAAU;oCACV,UAAU,MAAM,MAAM,IAAI;;sDAE1B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CACC,OAAM;4CACN,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;2BA7BA,KAAK,EAAE;;;;;kCAmCnB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;oBACtC,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4BAAoB,WAAU;;8CAC7B,8OAAC;oCACC,MAAK;oCACL,OAAO,OAAO,IAAI;oCAClB,UAAU,CAAC,IAAM,uBAAuB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAU;oCACV,aAAa,CAAC,OAAO,EAAE,QAAQ,GAAG;oCAClC,QAAQ;;;;;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,aAAa,OAAO,EAAE;oCACrC,WAAU;oCACV,UAAU,QAAQ,MAAM,IAAI;;sDAE5B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CACC,OAAM;4CACN,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;2BA7BA,OAAO,EAAE;;;;;kCAmCrB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAsB;;;;;;kCACvC,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;oBAI5C,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,OAAO,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO,KAAK,KAAK,EAAE;wBAE3D,qBACE,8OAAC;4BAAkB,WAAU;;8CAC3B,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG;;;;;;8CAEnC,8OAAC;oCAAI,WAAU;8CAAmB;;;;;;8CAClC,8OAAC;oCACC,OAAO,MAAM,aAAa;oCAC1B,UAAU,CAAC,IAAM,iBAAiB,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAU;oCACV,QAAQ;;sDAER,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;gDAAuB,OAAO,OAAO,EAAE;0DACrC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE,IAAI,GAAG;+CADjE,OAAO,EAAE;;;;;;;;;;;;2BAblB,KAAK,EAAE;;;;;oBAoBrB;;;;;;;;;;;;;AAIR"}}, {"offset": {"line": 1803, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/question-forms/FillInTheBlankForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { generateUUID } from \"@/lib/utils\";\n\ninterface FillInTheBlankFormProps {\n  data: any;\n  onChange: (data: any) => void;\n}\n\nexport default function FillInTheBlankForm({ data, onChange }: FillInTheBlankFormProps) {\n  const [textTemplate, setTextTemplate] = useState(\n    data?.text_template || \"This is a [BLANK] question with [BLANK] to fill in.\"\n  );\n  \n  const [blanks, setBlanks] = useState<Array<{\n    id: string;\n    correct_answers: string[];\n    case_sensitive?: boolean;\n    trim_whitespace?: boolean;\n    hint?: string;\n  }>>(\n    data?.blanks || []\n  );\n\n  // Initialize blanks based on template\n  useEffect(() => {\n    const blankCount = (textTemplate.match(/\\[BLANK\\]/g) || []).length;\n    \n    if (blanks.length !== blankCount) {\n      const newBlanks = Array(blankCount).fill(null).map((_, index) => {\n        // Reuse existing blank if available\n        if (index < blanks.length) {\n          return blanks[index];\n        }\n        \n        // Create new blank\n        return {\n          id: generateUUID(),\n          correct_answers: [\"\"],\n          case_sensitive: false,\n          trim_whitespace: true,\n          hint: \"\",\n        };\n      });\n      \n      setBlanks(newBlanks);\n    }\n  }, [textTemplate, blanks.length]);\n\n  // Update parent component when form data changes\n  useEffect(() => {\n    onChange({\n      text_template: textTemplate,\n      blanks,\n    });\n  }, [textTemplate, blanks, onChange]);\n\n  const handleBlankAnswerChange = (blankIndex: number, answerIndex: number, value: string) => {\n    const newBlanks = [...blanks];\n    if (newBlanks[blankIndex]) {\n      const newAnswers = [...newBlanks[blankIndex].correct_answers];\n      newAnswers[answerIndex] = value;\n      newBlanks[blankIndex] = {\n        ...newBlanks[blankIndex],\n        correct_answers: newAnswers,\n      };\n      setBlanks(newBlanks);\n    }\n  };\n\n  const handleBlankPropertyChange = (blankIndex: number, property: string, value: any) => {\n    const newBlanks = [...blanks];\n    if (newBlanks[blankIndex]) {\n      newBlanks[blankIndex] = {\n        ...newBlanks[blankIndex],\n        [property]: value,\n      };\n      setBlanks(newBlanks);\n    }\n  };\n\n  const addAnswerToBlank = (blankIndex: number) => {\n    const newBlanks = [...blanks];\n    if (newBlanks[blankIndex]) {\n      newBlanks[blankIndex] = {\n        ...newBlanks[blankIndex],\n        correct_answers: [...newBlanks[blankIndex].correct_answers, \"\"],\n      };\n      setBlanks(newBlanks);\n    }\n  };\n\n  const removeAnswerFromBlank = (blankIndex: number, answerIndex: number) => {\n    const newBlanks = [...blanks];\n    if (newBlanks[blankIndex] && newBlanks[blankIndex].correct_answers.length > 1) {\n      const newAnswers = [...newBlanks[blankIndex].correct_answers];\n      newAnswers.splice(answerIndex, 1);\n      newBlanks[blankIndex] = {\n        ...newBlanks[blankIndex],\n        correct_answers: newAnswers,\n      };\n      setBlanks(newBlanks);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-2\">\n        <label htmlFor=\"textTemplate\" className=\"text-sm font-medium\">\n          Question Text with Blanks\n        </label>\n        <p className=\"text-xs text-muted-foreground\">\n          Use [BLANK] to indicate where students should fill in answers.\n        </p>\n        <textarea\n          id=\"textTemplate\"\n          value={textTemplate}\n          onChange={(e) => setTextTemplate(e.target.value)}\n          className=\"w-full p-2 border rounded-md min-h-[100px]\"\n          placeholder=\"Enter text with [BLANK] placeholders\"\n          required\n        />\n      </div>\n\n      {blanks.length > 0 ? (\n        <div className=\"space-y-6\">\n          <h3 className=\"text-sm font-medium\">Define Blanks</h3>\n          \n          {blanks.map((blank, blankIndex) => (\n            <div key={blank.id} className=\"border rounded-md p-4 space-y-4\">\n              <h4 className=\"font-medium\">Blank {blankIndex + 1}</h4>\n              \n              <div className=\"space-y-4\">\n                <label className=\"text-sm font-medium\">Correct Answers</label>\n                {blank.correct_answers.map((answer, answerIndex) => (\n                  <div key={answerIndex} className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"text\"\n                      value={answer}\n                      onChange={(e) => handleBlankAnswerChange(blankIndex, answerIndex, e.target.value)}\n                      className=\"flex-1 p-2 border rounded-md\"\n                      placeholder=\"Enter a correct answer\"\n                      required\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => removeAnswerFromBlank(blankIndex, answerIndex)}\n                      className=\"h-8 w-8 p-0\"\n                      disabled={blank.correct_answers.length <= 1}\n                    >\n                      <span className=\"sr-only\">Remove</span>\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        className=\"h-4 w-4\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke=\"currentColor\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          strokeWidth={2}\n                          d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        />\n                      </svg>\n                    </Button>\n                  </div>\n                ))}\n                \n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => addAnswerToBlank(blankIndex)}\n                  className=\"w-full\"\n                >\n                  Add Another Correct Answer\n                </Button>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Answer Options</label>\n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={blank.case_sensitive}\n                      onChange={(e) => handleBlankPropertyChange(blankIndex, \"case_sensitive\", e.target.checked)}\n                    />\n                    <span>Case Sensitive</span>\n                  </label>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={blank.trim_whitespace}\n                      onChange={(e) => handleBlankPropertyChange(blankIndex, \"trim_whitespace\", e.target.checked)}\n                    />\n                    <span>Trim Whitespace</span>\n                  </label>\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label htmlFor={`hint-${blankIndex}`} className=\"text-sm font-medium\">\n                  Hint (Optional)\n                </label>\n                <input\n                  id={`hint-${blankIndex}`}\n                  type=\"text\"\n                  value={blank.hint || \"\"}\n                  onChange={(e) => handleBlankPropertyChange(blankIndex, \"hint\", e.target.value)}\n                  className=\"w-full p-2 border rounded-md\"\n                  placeholder=\"Enter a hint for this blank\"\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"p-4 bg-muted rounded-md\">\n          <p className=\"text-center\">\n            Add [BLANK] placeholders to your text to create blanks for students to fill in.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,mBAAmB,EAAE,IAAI,EAAE,QAAQ,EAA2B;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C,MAAM,iBAAiB;IAGzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAOjC,MAAM,UAAU,EAAE;IAGpB,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAC,aAAa,KAAK,CAAC,iBAAiB,EAAE,EAAE,MAAM;QAElE,IAAI,OAAO,MAAM,KAAK,YAAY;YAChC,MAAM,YAAY,MAAM,YAAY,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG;gBACrD,oCAAoC;gBACpC,IAAI,QAAQ,OAAO,MAAM,EAAE;oBACzB,OAAO,MAAM,CAAC,MAAM;gBACtB;gBAEA,mBAAmB;gBACnB,OAAO;oBACL,IAAI,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;oBACf,iBAAiB;wBAAC;qBAAG;oBACrB,gBAAgB;oBAChB,iBAAiB;oBACjB,MAAM;gBACR;YACF;YAEA,UAAU;QACZ;IACF,GAAG;QAAC;QAAc,OAAO,MAAM;KAAC;IAEhC,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP,eAAe;YACf;QACF;IACF,GAAG;QAAC;QAAc;QAAQ;KAAS;IAEnC,MAAM,0BAA0B,CAAC,YAAoB,aAAqB;QACxE,MAAM,YAAY;eAAI;SAAO;QAC7B,IAAI,SAAS,CAAC,WAAW,EAAE;YACzB,MAAM,aAAa;mBAAI,SAAS,CAAC,WAAW,CAAC,eAAe;aAAC;YAC7D,UAAU,CAAC,YAAY,GAAG;YAC1B,SAAS,CAAC,WAAW,GAAG;gBACtB,GAAG,SAAS,CAAC,WAAW;gBACxB,iBAAiB;YACnB;YACA,UAAU;QACZ;IACF;IAEA,MAAM,4BAA4B,CAAC,YAAoB,UAAkB;QACvE,MAAM,YAAY;eAAI;SAAO;QAC7B,IAAI,SAAS,CAAC,WAAW,EAAE;YACzB,SAAS,CAAC,WAAW,GAAG;gBACtB,GAAG,SAAS,CAAC,WAAW;gBACxB,CAAC,SAAS,EAAE;YACd;YACA,UAAU;QACZ;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY;eAAI;SAAO;QAC7B,IAAI,SAAS,CAAC,WAAW,EAAE;YACzB,SAAS,CAAC,WAAW,GAAG;gBACtB,GAAG,SAAS,CAAC,WAAW;gBACxB,iBAAiB;uBAAI,SAAS,CAAC,WAAW,CAAC,eAAe;oBAAE;iBAAG;YACjE;YACA,UAAU;QACZ;IACF;IAEA,MAAM,wBAAwB,CAAC,YAAoB;QACjD,MAAM,YAAY;eAAI;SAAO;QAC7B,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,GAAG,GAAG;YAC7E,MAAM,aAAa;mBAAI,SAAS,CAAC,WAAW,CAAC,eAAe;aAAC;YAC7D,WAAW,MAAM,CAAC,aAAa;YAC/B,SAAS,CAAC,WAAW,GAAG;gBACtB,GAAG,SAAS,CAAC,WAAW;gBACxB,iBAAiB;YACnB;YACA,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAe,WAAU;kCAAsB;;;;;;kCAG9D,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAG7C,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBAC/C,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;YAIX,OAAO,MAAM,GAAG,kBACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsB;;;;;;oBAEnC,OAAO,GAAG,CAAC,CAAC,OAAO,2BAClB,8OAAC;4BAAmB,WAAU;;8CAC5B,8OAAC;oCAAG,WAAU;;wCAAc;wCAAO,aAAa;;;;;;;8CAEhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;wCACtC,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAClC,8OAAC;gDAAsB,WAAU;;kEAC/B,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,wBAAwB,YAAY,aAAa,EAAE,MAAM,CAAC,KAAK;wDAChF,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;kEAEV,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,sBAAsB,YAAY;wDACjD,WAAU;wDACV,UAAU,MAAM,eAAe,CAAC,MAAM,IAAI;;0EAE1C,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;gEACC,OAAM;gEACN,WAAU;gEACV,MAAK;gEACL,SAAQ;gEACR,QAAO;0EAEP,cAAA,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAa;oEACb,GAAE;;;;;;;;;;;;;;;;;;+CA7BA;;;;;sDAoCZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;;;;;;;8CAKH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,MAAM,cAAc;wDAC7B,UAAU,CAAC,IAAM,0BAA0B,YAAY,kBAAkB,EAAE,MAAM,CAAC,OAAO;;;;;;kEAE3F,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAIV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,MAAM,eAAe;wDAC9B,UAAU,CAAC,IAAM,0BAA0B,YAAY,mBAAmB,EAAE,MAAM,CAAC,OAAO;;;;;;kEAE5F,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;8CAKZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAS,CAAC,KAAK,EAAE,YAAY;4CAAE,WAAU;sDAAsB;;;;;;sDAGtE,8OAAC;4CACC,IAAI,CAAC,KAAK,EAAE,YAAY;4CACxB,MAAK;4CACL,OAAO,MAAM,IAAI,IAAI;4CACrB,UAAU,CAAC,IAAM,0BAA0B,YAAY,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAC7E,WAAU;4CACV,aAAY;;;;;;;;;;;;;2BAvFR,MAAM,EAAE;;;;;;;;;;qCA8FtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAc;;;;;;;;;;;;;;;;;AAOrC"}}, {"offset": {"line": 2222, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/question-forms/EssayForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\n\ninterface EssayFormProps {\n  data: any;\n  onChange: (data: any) => void;\n}\n\nexport default function EssayForm({ data, onChange }: EssayFormProps) {\n  const [minWordCount, setMinWordCount] = useState(\n    data?.min_word_count?.toString() || \"\"\n  );\n  const [maxWordCount, setMaxWordCount] = useState(\n    data?.max_word_count?.toString() || \"\"\n  );\n  const [guidelines, setGuidelines] = useState(data?.guidelines || \"\");\n\n  // Update parent component when form data changes\n  useEffect(() => {\n    onChange({\n      min_word_count: minWordCount ? parseInt(minWordCount) : undefined,\n      max_word_count: maxWordCount ? parseInt(maxWordCount) : undefined,\n      guidelines: guidelines || undefined,\n    });\n  }, [minWordCount, maxWordCount, guidelines, onChange]);\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div className=\"space-y-2\">\n          <label htmlFor=\"minWordCount\" className=\"text-sm font-medium\">\n            Minimum Word Count\n          </label>\n          <input\n            id=\"minWordCount\"\n            type=\"number\"\n            min=\"0\"\n            value={minWordCount}\n            onChange={(e) => setMinWordCount(e.target.value)}\n            className=\"w-full p-2 border rounded-md\"\n            placeholder=\"Optional\"\n          />\n        </div>\n        \n        <div className=\"space-y-2\">\n          <label htmlFor=\"maxWordCount\" className=\"text-sm font-medium\">\n            Maximum Word Count\n          </label>\n          <input\n            id=\"maxWordCount\"\n            type=\"number\"\n            min=\"0\"\n            value={maxWordCount}\n            onChange={(e) => setMaxWordCount(e.target.value)}\n            className=\"w-full p-2 border rounded-md\"\n            placeholder=\"Optional\"\n          />\n        </div>\n      </div>\n      \n      <div className=\"space-y-2\">\n        <label htmlFor=\"guidelines\" className=\"text-sm font-medium\">\n          Guidelines for Students\n        </label>\n        <textarea\n          id=\"guidelines\"\n          value={guidelines}\n          onChange={(e) => setGuidelines(e.target.value)}\n          className=\"w-full p-2 border rounded-md min-h-[100px]\"\n          placeholder=\"Enter guidelines or prompts for the essay (optional)\"\n        />\n      </div>\n      \n      <div className=\"p-4 bg-muted rounded-md\">\n        <p className=\"text-sm\">\n          <strong>Note:</strong> Essay questions typically require manual grading. Students will see the guidelines and word count limits when answering the question.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAkB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C,MAAM,gBAAgB,cAAc;IAEtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C,MAAM,gBAAgB,cAAc;IAEtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,cAAc;IAEjE,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP,gBAAgB,eAAe,SAAS,gBAAgB;YACxD,gBAAgB,eAAe,SAAS,gBAAgB;YACxD,YAAY,cAAc;QAC5B;IACF,GAAG;QAAC;QAAc;QAAc;QAAY;KAAS;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAsB;;;;;;0CAG9D,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAsB;;;;;;0CAG9D,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAKlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAa,WAAU;kCAAsB;;;;;;kCAG5D,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAAc;;;;;;;;;;;;;;;;;;AAKhC"}}, {"offset": {"line": 2387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuestionForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Question } from \"@/generated/prisma\";\nimport { Button } from \"@/components/ui/button\";\nimport { generateUUID } from \"@/lib/utils\";\n\n// Form components for different question types\nimport MultipleChoiceForm from \"./question-forms/MultipleChoiceForm\";\nimport TrueFalseForm from \"./question-forms/TrueFalseForm\";\nimport ShortAnswerForm from \"./question-forms/ShortAnswerForm\";\nimport MatchingForm from \"./question-forms/MatchingForm\";\nimport FillInTheBlankForm from \"./question-forms/FillInTheBlankForm\";\nimport EssayForm from \"./question-forms/EssayForm\";\n\ninterface QuestionFormProps {\n  questionType: string;\n  initialData?: any;\n  onSubmit: (questionData: any) => void;\n  isSaving: boolean;\n  submitLabel: string;\n}\n\nexport default function QuestionForm({\n  questionType,\n  initialData,\n  onSubmit,\n  isSaving,\n  submitLabel,\n}: QuestionFormProps) {\n  const [questionText, setQuestionText] = useState(\n    initialData?.text\n      ? typeof initialData.text === \"string\"\n        ? initialData.text\n        : JSON.parse(initialData.text).default || \"\"\n      : \"\"\n  );\n  const [points, setPoints] = useState(initialData?.points?.toString() || \"1\");\n  const [feedbackCorrect, setFeedbackCorrect] = useState(initialData?.feedbackCorrect || \"\");\n  const [feedbackIncorrect, setFeedbackIncorrect] = useState(initialData?.feedbackIncorrect || \"\");\n  \n  // Type-specific state\n  const [typeSpecificData, setTypeSpecificData] = useState<any>(\n    initialData ? { ...initialData } : {}\n  );\n\n  // Reset form when question type changes\n  useEffect(() => {\n    if (!initialData) {\n      setQuestionText(\"\");\n      setPoints(\"1\");\n      setFeedbackCorrect(\"\");\n      setFeedbackIncorrect(\"\");\n      setTypeSpecificData({});\n    }\n  }, [questionType, initialData]);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Prepare common question data\n    const questionData = {\n      text: questionText,\n      points: parseFloat(points),\n      feedbackCorrect: feedbackCorrect || null,\n      feedbackIncorrect: feedbackIncorrect || null,\n      ...typeSpecificData,\n    };\n    \n    onSubmit(questionData);\n  };\n\n  const renderTypeSpecificForm = () => {\n    switch (questionType) {\n      case \"multiple_choice\":\n        return (\n          <MultipleChoiceForm\n            data={typeSpecificData}\n            onChange={setTypeSpecificData}\n          />\n        );\n      case \"true_false\":\n        return (\n          <TrueFalseForm\n            data={typeSpecificData}\n            onChange={setTypeSpecificData}\n          />\n        );\n      case \"short_answer\":\n        return (\n          <ShortAnswerForm\n            data={typeSpecificData}\n            onChange={setTypeSpecificData}\n          />\n        );\n      case \"matching\":\n        return (\n          <MatchingForm\n            data={typeSpecificData}\n            onChange={setTypeSpecificData}\n          />\n        );\n      case \"fill_in_the_blank\":\n        return (\n          <FillInTheBlankForm\n            data={typeSpecificData}\n            onChange={setTypeSpecificData}\n          />\n        );\n      case \"essay\":\n        return (\n          <EssayForm\n            data={typeSpecificData}\n            onChange={setTypeSpecificData}\n          />\n        );\n      default:\n        return <p>Unsupported question type: {questionType}</p>;\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"space-y-2\">\n        <label htmlFor=\"questionText\" className=\"text-sm font-medium\">\n          Question Text\n        </label>\n        <textarea\n          id=\"questionText\"\n          value={questionText}\n          onChange={(e) => setQuestionText(e.target.value)}\n          className=\"w-full p-2 border rounded-md min-h-[100px]\"\n          placeholder=\"Enter your question here...\"\n          required\n        />\n      </div>\n      \n      <div className=\"space-y-2\">\n        <label htmlFor=\"points\" className=\"text-sm font-medium\">\n          Points\n        </label>\n        <input\n          id=\"points\"\n          type=\"number\"\n          min=\"0.5\"\n          step=\"0.5\"\n          value={points}\n          onChange={(e) => setPoints(e.target.value)}\n          className=\"w-full p-2 border rounded-md\"\n          required\n        />\n      </div>\n      \n      {renderTypeSpecificForm()}\n      \n      <div className=\"space-y-2\">\n        <label htmlFor=\"feedbackCorrect\" className=\"text-sm font-medium\">\n          Feedback for Correct Answer\n        </label>\n        <textarea\n          id=\"feedbackCorrect\"\n          value={feedbackCorrect}\n          onChange={(e) => setFeedbackCorrect(e.target.value)}\n          className=\"w-full p-2 border rounded-md\"\n          placeholder=\"Feedback to show when the answer is correct\"\n        />\n      </div>\n      \n      <div className=\"space-y-2\">\n        <label htmlFor=\"feedbackIncorrect\" className=\"text-sm font-medium\">\n          Feedback for Incorrect Answer\n        </label>\n        <textarea\n          id=\"feedbackIncorrect\"\n          value={feedbackIncorrect}\n          onChange={(e) => setFeedbackIncorrect(e.target.value)}\n          className=\"w-full p-2 border rounded-md\"\n          placeholder=\"Feedback to show when the answer is incorrect\"\n        />\n      </div>\n      \n      <div className=\"flex justify-end pt-4\">\n        <Button type=\"submit\" disabled={isSaving}>\n          {isSaving ? \"Saving...\" : submitLabel}\n        </Button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAGA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;AAuBe,SAAS,aAAa,EACnC,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,WAAW,EACO;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C,aAAa,OACT,OAAO,YAAY,IAAI,KAAK,WAC1B,YAAY,IAAI,GAChB,KAAK,KAAK,CAAC,YAAY,IAAI,EAAE,OAAO,IAAI,KAC1C;IAEN,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,QAAQ,cAAc;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,mBAAmB;IACvF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,qBAAqB;IAE7F,sBAAsB;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrD,cAAc;QAAE,GAAG,WAAW;IAAC,IAAI,CAAC;IAGtC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;YAChB,gBAAgB;YAChB,UAAU;YACV,mBAAmB;YACnB,qBAAqB;YACrB,oBAAoB,CAAC;QACvB;IACF,GAAG;QAAC;QAAc;KAAY;IAE9B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,+BAA+B;QAC/B,MAAM,eAAe;YACnB,MAAM;YACN,QAAQ,WAAW;YACnB,iBAAiB,mBAAmB;YACpC,mBAAmB,qBAAqB;YACxC,GAAG,gBAAgB;QACrB;QAEA,SAAS;IACX;IAEA,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,+KAAA,CAAA,UAAkB;oBACjB,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,0KAAA,CAAA,UAAa;oBACZ,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,4KAAA,CAAA,UAAe;oBACd,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,yKAAA,CAAA,UAAY;oBACX,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,+KAAA,CAAA,UAAkB;oBACjB,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,sKAAA,CAAA,UAAS;oBACR,MAAM;oBACN,UAAU;;;;;;YAGhB;gBACE,qBAAO,8OAAC;;wBAAE;wBAA4B;;;;;;;QAC1C;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAe,WAAU;kCAAsB;;;;;;kCAG9D,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBAC/C,WAAU;wBACV,aAAY;wBACZ,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAS,WAAU;kCAAsB;;;;;;kCAGxD,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,KAAI;wBACJ,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wBACzC,WAAU;wBACV,QAAQ;;;;;;;;;;;;YAIX;0BAED,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAkB,WAAU;kCAAsB;;;;;;kCAGjE,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wBAClD,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAoB,WAAU;kCAAsB;;;;;;kCAGnE,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACpD,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAS,UAAU;8BAC7B,WAAW,cAAc;;;;;;;;;;;;;;;;;AAKpC"}}, {"offset": {"line": 2667, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2673, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuestionsManager.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Question } from \"@/generated/prisma\";\nimport { Button } from \"@/components/ui/button\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport { generateUUID } from \"@/lib/utils\";\nimport QuestionForm from \"./QuestionForm\";\n\ninterface QuestionsManagerProps {\n  questions: Question[];\n  onAddQuestion: (newQuestion: Omit<Question, \"id\" | \"quizId\" | \"createdAt\" | \"updatedAt\">) => void;\n  onUpdateQuestion: (questionId: string, updatedQuestion: Partial<Question>) => void;\n  onDeleteQuestion: (questionId: string) => void;\n  isSaving: boolean;\n}\n\nexport default function QuestionsManager({\n  questions,\n  onAddQuestion,\n  onUpdateQuestion,\n  onDeleteQuestion,\n  isSaving,\n}: QuestionsManagerProps) {\n  const [activeTab, setActiveTab] = useState(\"existing\");\n  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);\n  const [questionType, setQuestionType] = useState<string>(\"multiple_choice\");\n\n  const handleAddQuestion = (questionData: any) => {\n    const newQuestion = {\n      questionId: generateUUID(),\n      type: questionType,\n      ...questionData,\n    };\n\n    onAddQuestion(newQuestion);\n    setActiveTab(\"existing\");\n  };\n\n  const handleUpdateQuestion = (questionData: any) => {\n    if (selectedQuestionId) {\n      onUpdateQuestion(selectedQuestionId, questionData);\n      setSelectedQuestionId(null);\n    }\n  };\n\n  const handleDeleteQuestion = (questionId: string) => {\n    if (confirm(\"Are you sure you want to delete this question?\")) {\n      onDeleteQuestion(questionId);\n      if (selectedQuestionId === questionId) {\n        setSelectedQuestionId(null);\n      }\n    }\n  };\n\n  const selectedQuestion = selectedQuestionId\n    ? questions.find((q) => q.id === selectedQuestionId)\n    : null;\n\n  return (\n    <div className=\"space-y-6\">\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid grid-cols-2 w-full max-w-md\">\n          <TabsTrigger value=\"existing\">Existing Questions</TabsTrigger>\n          <TabsTrigger value=\"add\">Add Question</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"existing\">\n          <div className=\"space-y-6\">\n            {questions.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <p className=\"text-muted-foreground mb-4\">\n                  No questions yet. Add your first question to get started.\n                </p>\n                <Button onClick={() => setActiveTab(\"add\")}>\n                  Add First Question\n                </Button>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"border rounded-md p-4 h-[500px] overflow-y-auto\">\n                  <h3 className=\"font-medium mb-4\">Questions ({questions.length})</h3>\n                  <ul className=\"space-y-2\">\n                    {questions.map((question) => (\n                      <li\n                        key={question.id}\n                        className={`p-3 border rounded-md cursor-pointer transition-colors ${\n                          selectedQuestionId === question.id\n                            ? \"border-primary bg-primary/5\"\n                            : \"hover:border-primary/50\"\n                        }`}\n                        onClick={() => setSelectedQuestionId(question.id)}\n                      >\n                        <div className=\"flex justify-between items-start\">\n                          <div>\n                            <p className=\"font-medium line-clamp-1\">\n                              {typeof question.text === \"string\"\n                                ? question.text\n                                : (question.text as any)?.default || \"Question\"}\n                            </p>\n                            <p className=\"text-xs text-muted-foreground\">\n                              {question.type} • {question.points} points\n                            </p>\n                          </div>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-8 w-8 p-0\"\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              handleDeleteQuestion(question.id);\n                            }}\n                          >\n                            <span className=\"sr-only\">Delete</span>\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              className=\"h-4 w-4\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke=\"currentColor\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                strokeWidth={2}\n                                d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                              />\n                            </svg>\n                          </Button>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                <div className=\"border rounded-md p-4 h-[500px] overflow-y-auto\">\n                  {selectedQuestion ? (\n                    <div>\n                      <h3 className=\"font-medium mb-4\">Edit Question</h3>\n                      <QuestionForm\n                        questionType={selectedQuestion.type}\n                        initialData={selectedQuestion}\n                        onSubmit={handleUpdateQuestion}\n                        isSaving={isSaving}\n                        submitLabel=\"Update Question\"\n                      />\n                    </div>\n                  ) : (\n                    <div className=\"flex items-center justify-center h-full\">\n                      <p className=\"text-muted-foreground\">\n                        Select a question to edit\n                      </p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n        </TabsContent>\n\n        <TabsContent value=\"add\">\n          <div className=\"space-y-6\">\n            <div className=\"space-y-2\">\n              <label htmlFor=\"questionType\" className=\"text-sm font-medium\">\n                Question Type\n              </label>\n              <select\n                id=\"questionType\"\n                value={questionType}\n                onChange={(e) => setQuestionType(e.target.value)}\n                className=\"w-full p-2 border rounded-md\"\n              >\n                <option value=\"multiple_choice\">Multiple Choice</option>\n                <option value=\"true_false\">True/False</option>\n                <option value=\"short_answer\">Short Answer</option>\n                <option value=\"matching\">Matching</option>\n                <option value=\"fill_in_the_blank\">Fill in the Blank</option>\n                <option value=\"essay\">Essay</option>\n              </select>\n            </div>\n\n            <div className=\"border rounded-md p-4\">\n              <h3 className=\"font-medium mb-4\">Add New Question</h3>\n              <QuestionForm\n                questionType={questionType}\n                onSubmit={handleAddQuestion}\n                isSaving={isSaving}\n                submitLabel=\"Add Question\"\n              />\n            </div>\n          </div>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AAPA;;;;;;;AAiBe,SAAS,iBAAiB,EACvC,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACc;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc;YAClB,YAAY,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;YACvB,MAAM;YACN,GAAG,YAAY;QACjB;QAEA,cAAc;QACd,aAAa;IACf;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,oBAAoB;YACtB,iBAAiB,oBAAoB;YACrC,sBAAsB;QACxB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,QAAQ,mDAAmD;YAC7D,iBAAiB;YACjB,IAAI,uBAAuB,YAAY;gBACrC,sBAAsB;YACxB;QACF;IACF;IAEA,MAAM,mBAAmB,qBACrB,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,sBAC/B;IAEJ,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,OAAO;YAAW,eAAe;;8BACrC,8OAAC,gIAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;sCAAW;;;;;;sCAC9B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;sCAAM;;;;;;;;;;;;8BAG3B,8OAAC,gIAAA,CAAA,cAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC;wBAAI,WAAU;kCACZ,UAAU,MAAM,KAAK,kBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,aAAa;8CAAQ;;;;;;;;;;;iDAK9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAmB;gDAAY,UAAU,MAAM;gDAAC;;;;;;;sDAC9D,8OAAC;4CAAG,WAAU;sDACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;oDAEC,WAAW,CAAC,uDAAuD,EACjE,uBAAuB,SAAS,EAAE,GAC9B,gCACA,2BACJ;oDACF,SAAS,IAAM,sBAAsB,SAAS,EAAE;8DAEhD,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFACV,OAAO,SAAS,IAAI,KAAK,WACtB,SAAS,IAAI,GACb,AAAC,SAAS,IAAI,EAAU,WAAW;;;;;;kFAEzC,8OAAC;wEAAE,WAAU;;4EACV,SAAS,IAAI;4EAAC;4EAAI,SAAS,MAAM;4EAAC;;;;;;;;;;;;;0EAGvC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,qBAAqB,SAAS,EAAE;gEAClC;;kFAEA,8OAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,8OAAC;wEACC,OAAM;wEACN,WAAU;wEACV,MAAK;wEACL,SAAQ;wEACR,QAAO;kFAEP,cAAA,8OAAC;4EACC,eAAc;4EACd,gBAAe;4EACf,aAAa;4EACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;mDAxCL,SAAS,EAAE;;;;;;;;;;;;;;;;8CAkDxB,8OAAC;oCAAI,WAAU;8CACZ,iCACC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC,oJAAA,CAAA,UAAY;gDACX,cAAc,iBAAiB,IAAI;gDACnC,aAAa;gDACb,UAAU;gDACV,UAAU;gDACV,aAAY;;;;;;;;;;;6DAIhB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWnD,8OAAC,gIAAA,CAAA,cAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAe,WAAU;kDAAsB;;;;;;kDAG9D,8OAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAkB;;;;;;0DAChC,8OAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8OAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAoB;;;;;;0DAClC,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAI1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,8OAAC,oJAAA,CAAA,UAAY;wCACX,cAAc;wCACd,UAAU;wCACV,UAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B"}}, {"offset": {"line": 3094, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/PoolsManager.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Quiz, Question, QuestionPool, SelectionRule } from \"@/generated/prisma\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport { generateUUID } from \"@/lib/utils\";\n\ntype QuizWithRelations = Quiz & {\n  questions: Question[];\n  questionPools: (QuestionPool & {\n    questions: Question[];\n  })[];\n  selectionRules: SelectionRule[];\n};\n\ninterface PoolsManagerProps {\n  quiz: QuizWithRelations;\n  setQuiz: React.Dispatch<React.SetStateAction<QuizWithRelations>>;\n  isSaving: boolean;\n}\n\nexport default function PoolsManager({ quiz, setQuiz, isSaving }: PoolsManagerProps) {\n  const [activeTab, setActiveTab] = useState(\"pools\");\n  const [selectedPoolId, setSelectedPoolId] = useState<string | null>(null);\n  const [newPoolTitle, setNewPoolTitle] = useState(\"\");\n  const [newPoolDescription, setNewPoolDescription] = useState(\"\");\n  const [editingPool, setEditingPool] = useState<{\n    id: string;\n    title: string;\n    description: string;\n  } | null>(null);\n  const [savingPool, setSavingPool] = useState(false);\n\n  // Selection rules state\n  const [selectedRuleId, setSelectedRuleId] = useState<string | null>(null);\n  const [newRulePoolId, setNewRulePoolId] = useState(\"\");\n  const [newRuleSelectCount, setNewRuleSelectCount] = useState(\"1\");\n  const [newRuleRandomize, setNewRuleRandomize] = useState(true);\n  const [newRuleShuffleOrder, setNewRuleShuffleOrder] = useState(false);\n  const [editingRule, setEditingRule] = useState<{\n    id: string;\n    poolId: string;\n    selectCount: number;\n    randomize: boolean;\n    shuffleOrder: boolean;\n  } | null>(null);\n  const [savingRule, setSavingRule] = useState(false);\n\n  // Pool questions management\n  const [availableQuestions, setAvailableQuestions] = useState<Question[]>([]);\n  const [poolQuestions, setPoolQuestions] = useState<Question[]>([]);\n\n  // Load pool details when a pool is selected\n  const handleSelectPool = async (poolId: string) => {\n    setSelectedPoolId(poolId);\n    setSelectedRuleId(null);\n    \n    const pool = quiz.questionPools.find(p => p.id === poolId);\n    if (pool) {\n      setEditingPool({\n        id: pool.id,\n        title: pool.title || \"\",\n        description: pool.description || \"\",\n      });\n      \n      // Load questions for this pool\n      setPoolQuestions(pool.questions);\n      \n      // Set available questions (those not in any pool)\n      const questionsInPools = quiz.questionPools.flatMap(p => p.questions.map(q => q.id));\n      setAvailableQuestions(quiz.questions.filter(q => !questionsInPools.includes(q.id)));\n    }\n  };\n\n  // Load rule details when a rule is selected\n  const handleSelectRule = (ruleId: string) => {\n    setSelectedRuleId(ruleId);\n    setSelectedPoolId(null);\n    \n    const rule = quiz.selectionRules.find(r => r.id === ruleId);\n    if (rule) {\n      setEditingRule({\n        id: rule.id,\n        poolId: rule.poolId,\n        selectCount: rule.selectCount,\n        randomize: rule.randomize,\n        shuffleOrder: rule.shuffleOrder,\n      });\n    }\n  };\n\n  // Create a new pool\n  const handleCreatePool = async () => {\n    setSavingPool(true);\n    \n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/pools`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          poolId: generateUUID(),\n          title: newPoolTitle,\n          description: newPoolDescription,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to create pool\");\n      }\n\n      const newPool = await response.json();\n      \n      setQuiz(prev => ({\n        ...prev,\n        questionPools: [...prev.questionPools, { ...newPool, questions: [] }],\n      }));\n      \n      setNewPoolTitle(\"\");\n      setNewPoolDescription(\"\");\n      setActiveTab(\"pools\");\n    } catch (error) {\n      console.error(\"Error creating pool:\", error);\n    } finally {\n      setSavingPool(false);\n    }\n  };\n\n  // Update an existing pool\n  const handleUpdatePool = async () => {\n    if (!editingPool) return;\n    \n    setSavingPool(true);\n    \n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/pools/${editingPool.id}`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          title: editingPool.title,\n          description: editingPool.description,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update pool\");\n      }\n\n      const updatedPool = await response.json();\n      \n      setQuiz(prev => ({\n        ...prev,\n        questionPools: prev.questionPools.map(pool => \n          pool.id === editingPool.id \n            ? { ...pool, ...updatedPool } \n            : pool\n        ),\n      }));\n    } catch (error) {\n      console.error(\"Error updating pool:\", error);\n    } finally {\n      setSavingPool(false);\n    }\n  };\n\n  // Delete a pool\n  const handleDeletePool = async (poolId: string) => {\n    if (!confirm(\"Are you sure you want to delete this pool? This will also delete any selection rules that use this pool.\")) {\n      return;\n    }\n    \n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/pools/${poolId}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to delete pool\");\n      }\n      \n      setQuiz(prev => ({\n        ...prev,\n        questionPools: prev.questionPools.filter(pool => pool.id !== poolId),\n        selectionRules: prev.selectionRules.filter(rule => rule.poolId !== poolId),\n      }));\n      \n      if (selectedPoolId === poolId) {\n        setSelectedPoolId(null);\n        setEditingPool(null);\n      }\n    } catch (error) {\n      console.error(\"Error deleting pool:\", error);\n    }\n  };\n\n  // Create a new selection rule\n  const handleCreateRule = async () => {\n    setSavingRule(true);\n    \n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/rules`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          poolId: newRulePoolId,\n          selectCount: parseInt(newRuleSelectCount),\n          randomize: newRuleRandomize,\n          shuffleOrder: newRuleShuffleOrder,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to create rule\");\n      }\n\n      const newRule = await response.json();\n      \n      setQuiz(prev => ({\n        ...prev,\n        selectionRules: [...prev.selectionRules, newRule],\n      }));\n      \n      setNewRulePoolId(\"\");\n      setNewRuleSelectCount(\"1\");\n      setNewRuleRandomize(true);\n      setNewRuleShuffleOrder(false);\n      setActiveTab(\"rules\");\n    } catch (error) {\n      console.error(\"Error creating rule:\", error);\n    } finally {\n      setSavingRule(false);\n    }\n  };\n\n  // Update an existing rule\n  const handleUpdateRule = async () => {\n    if (!editingRule) return;\n    \n    setSavingRule(true);\n    \n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/rules/${editingRule.id}`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          poolId: editingRule.poolId,\n          selectCount: editingRule.selectCount,\n          randomize: editingRule.randomize,\n          shuffleOrder: editingRule.shuffleOrder,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update rule\");\n      }\n\n      const updatedRule = await response.json();\n      \n      setQuiz(prev => ({\n        ...prev,\n        selectionRules: prev.selectionRules.map(rule => \n          rule.id === editingRule.id \n            ? { ...rule, ...updatedRule } \n            : rule\n        ),\n      }));\n    } catch (error) {\n      console.error(\"Error updating rule:\", error);\n    } finally {\n      setSavingRule(false);\n    }\n  };\n\n  // Delete a rule\n  const handleDeleteRule = async (ruleId: string) => {\n    if (!confirm(\"Are you sure you want to delete this selection rule?\")) {\n      return;\n    }\n    \n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/rules/${ruleId}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to delete rule\");\n      }\n      \n      setQuiz(prev => ({\n        ...prev,\n        selectionRules: prev.selectionRules.filter(rule => rule.id !== ruleId),\n      }));\n      \n      if (selectedRuleId === ruleId) {\n        setSelectedRuleId(null);\n        setEditingRule(null);\n      }\n    } catch (error) {\n      console.error(\"Error deleting rule:\", error);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid grid-cols-3 w-full max-w-md\">\n          <TabsTrigger value=\"pools\">Question Pools</TabsTrigger>\n          <TabsTrigger value=\"rules\">Selection Rules</TabsTrigger>\n          <TabsTrigger value=\"create\">Create New</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"pools\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"border rounded-md p-4 h-[500px] overflow-y-auto\">\n              <h3 className=\"font-medium mb-4\">Pools ({quiz.questionPools.length})</h3>\n              \n              {quiz.questionPools.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <p className=\"text-muted-foreground mb-4\">\n                    No question pools yet. Create your first pool to get started.\n                  </p>\n                  <Button onClick={() => setActiveTab(\"create\")}>\n                    Create First Pool\n                  </Button>\n                </div>\n              ) : (\n                <ul className=\"space-y-2\">\n                  {quiz.questionPools.map((pool) => (\n                    <li\n                      key={pool.id}\n                      className={`p-3 border rounded-md cursor-pointer transition-colors ${\n                        selectedPoolId === pool.id\n                          ? \"border-primary bg-primary/5\"\n                          : \"hover:border-primary/50\"\n                      }`}\n                      onClick={() => handleSelectPool(pool.id)}\n                    >\n                      <div className=\"flex justify-between items-start\">\n                        <div>\n                          <p className=\"font-medium\">{pool.title || `Pool ${pool.poolId}`}</p>\n                          <p className=\"text-xs text-muted-foreground\">\n                            {pool.questions.length} questions\n                          </p>\n                        </div>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          className=\"h-8 w-8 p-0\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            handleDeletePool(pool.id);\n                          }}\n                        >\n                          <span className=\"sr-only\">Delete</span>\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            className=\"h-4 w-4\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke=\"currentColor\"\n                          >\n                            <path\n                              strokeLinecap=\"round\"\n                              strokeLinejoin=\"round\"\n                              strokeWidth={2}\n                              d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            />\n                          </svg>\n                        </Button>\n                      </div>\n                    </li>\n                  ))}\n                </ul>\n              )}\n            </div>\n\n            <div className=\"border rounded-md p-4 h-[500px] overflow-y-auto\">\n              {selectedPoolId && editingPool ? (\n                <div className=\"space-y-4\">\n                  <h3 className=\"font-medium\">Edit Pool</h3>\n                  \n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"poolTitle\" className=\"text-sm font-medium\">\n                      Pool Title\n                    </label>\n                    <input\n                      id=\"poolTitle\"\n                      type=\"text\"\n                      value={editingPool.title}\n                      onChange={(e) => setEditingPool({...editingPool, title: e.target.value})}\n                      className=\"w-full p-2 border rounded-md\"\n                      placeholder=\"Enter pool title\"\n                    />\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"poolDescription\" className=\"text-sm font-medium\">\n                      Description\n                    </label>\n                    <textarea\n                      id=\"poolDescription\"\n                      value={editingPool.description}\n                      onChange={(e) => setEditingPool({...editingPool, description: e.target.value})}\n                      className=\"w-full p-2 border rounded-md\"\n                      placeholder=\"Enter pool description\"\n                    />\n                  </div>\n                  \n                  <div className=\"flex justify-end\">\n                    <Button\n                      onClick={handleUpdatePool}\n                      disabled={savingPool}\n                    >\n                      {savingPool ? \"Saving...\" : \"Save Pool\"}\n                    </Button>\n                  </div>\n                  \n                  <div className=\"pt-4 border-t mt-4\">\n                    <h4 className=\"font-medium mb-2\">Questions in this Pool</h4>\n                    {/* Pool questions management would go here */}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center h-full\">\n                  <p className=\"text-muted-foreground\">\n                    Select a pool to edit\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </TabsContent>\n\n        <TabsContent value=\"rules\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"border rounded-md p-4 h-[500px] overflow-y-auto\">\n              <h3 className=\"font-medium mb-4\">Selection Rules ({quiz.selectionRules.length})</h3>\n              \n              {quiz.selectionRules.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <p className=\"text-muted-foreground mb-4\">\n                    No selection rules yet. Create your first rule to get started.\n                  </p>\n                  <Button onClick={() => setActiveTab(\"create\")}>\n                    Create First Rule\n                  </Button>\n                </div>\n              ) : (\n                <ul className=\"space-y-2\">\n                  {quiz.selectionRules.map((rule) => {\n                    const pool = quiz.questionPools.find(p => p.id === rule.poolId);\n                    \n                    return (\n                      <li\n                        key={rule.id}\n                        className={`p-3 border rounded-md cursor-pointer transition-colors ${\n                          selectedRuleId === rule.id\n                            ? \"border-primary bg-primary/5\"\n                            : \"hover:border-primary/50\"\n                        }`}\n                        onClick={() => handleSelectRule(rule.id)}\n                      >\n                        <div className=\"flex justify-between items-start\">\n                          <div>\n                            <p className=\"font-medium\">\n                              Select {rule.selectCount} from {pool?.title || rule.poolId}\n                            </p>\n                            <p className=\"text-xs text-muted-foreground\">\n                              {rule.randomize ? \"Random selection\" : \"Sequential selection\"}\n                              {rule.shuffleOrder ? \", shuffled order\" : \"\"}\n                            </p>\n                          </div>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-8 w-8 p-0\"\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              handleDeleteRule(rule.id);\n                            }}\n                          >\n                            <span className=\"sr-only\">Delete</span>\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              className=\"h-4 w-4\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke=\"currentColor\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                strokeWidth={2}\n                                d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                              />\n                            </svg>\n                          </Button>\n                        </div>\n                      </li>\n                    );\n                  })}\n                </ul>\n              )}\n            </div>\n\n            <div className=\"border rounded-md p-4 h-[500px] overflow-y-auto\">\n              {selectedRuleId && editingRule ? (\n                <div className=\"space-y-4\">\n                  <h3 className=\"font-medium\">Edit Selection Rule</h3>\n                  \n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"rulePool\" className=\"text-sm font-medium\">\n                      Question Pool\n                    </label>\n                    <select\n                      id=\"rulePool\"\n                      value={editingRule.poolId}\n                      onChange={(e) => setEditingRule({...editingRule, poolId: e.target.value})}\n                      className=\"w-full p-2 border rounded-md\"\n                      required\n                    >\n                      <option value=\"\">-- Select a pool --</option>\n                      {quiz.questionPools.map((pool) => (\n                        <option key={pool.id} value={pool.id}>\n                          {pool.title || `Pool ${pool.poolId}`} ({pool.questions.length} questions)\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <label htmlFor=\"ruleSelectCount\" className=\"text-sm font-medium\">\n                      Number of Questions to Select\n                    </label>\n                    <input\n                      id=\"ruleSelectCount\"\n                      type=\"number\"\n                      min=\"1\"\n                      value={editingRule.selectCount}\n                      onChange={(e) => setEditingRule({...editingRule, selectCount: parseInt(e.target.value)})}\n                      className=\"w-full p-2 border rounded-md\"\n                      required\n                    />\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={editingRule.randomize}\n                        onChange={(e) => setEditingRule({...editingRule, randomize: e.target.checked})}\n                      />\n                      <span>Randomize Selection</span>\n                    </label>\n                    <p className=\"text-xs text-muted-foreground ml-6\">\n                      If checked, questions will be randomly selected from the pool.\n                    </p>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={editingRule.shuffleOrder}\n                        onChange={(e) => setEditingRule({...editingRule, shuffleOrder: e.target.checked})}\n                      />\n                      <span>Shuffle Order</span>\n                    </label>\n                    <p className=\"text-xs text-muted-foreground ml-6\">\n                      If checked, the order of selected questions will be randomized.\n                    </p>\n                  </div>\n                  \n                  <div className=\"flex justify-end\">\n                    <Button\n                      onClick={handleUpdateRule}\n                      disabled={savingRule}\n                    >\n                      {savingRule ? \"Saving...\" : \"Save Rule\"}\n                    </Button>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center h-full\">\n                  <p className=\"text-muted-foreground\">\n                    Select a rule to edit\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </TabsContent>\n\n        <TabsContent value=\"create\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"border rounded-md p-4\">\n              <h3 className=\"font-medium mb-4\">Create New Pool</h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"newPoolTitle\" className=\"text-sm font-medium\">\n                    Pool Title\n                  </label>\n                  <input\n                    id=\"newPoolTitle\"\n                    type=\"text\"\n                    value={newPoolTitle}\n                    onChange={(e) => setNewPoolTitle(e.target.value)}\n                    className=\"w-full p-2 border rounded-md\"\n                    placeholder=\"Enter pool title\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label htmlFor=\"newPoolDescription\" className=\"text-sm font-medium\">\n                    Description\n                  </label>\n                  <textarea\n                    id=\"newPoolDescription\"\n                    value={newPoolDescription}\n                    onChange={(e) => setNewPoolDescription(e.target.value)}\n                    className=\"w-full p-2 border rounded-md\"\n                    placeholder=\"Enter pool description (optional)\"\n                  />\n                </div>\n                \n                <div className=\"flex justify-end\">\n                  <Button\n                    onClick={handleCreatePool}\n                    disabled={!newPoolTitle || savingPool}\n                  >\n                    {savingPool ? \"Creating...\" : \"Create Pool\"}\n                  </Button>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"border rounded-md p-4\">\n              <h3 className=\"font-medium mb-4\">Create New Selection Rule</h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"newRulePool\" className=\"text-sm font-medium\">\n                    Question Pool\n                  </label>\n                  <select\n                    id=\"newRulePool\"\n                    value={newRulePoolId}\n                    onChange={(e) => setNewRulePoolId(e.target.value)}\n                    className=\"w-full p-2 border rounded-md\"\n                    required\n                  >\n                    <option value=\"\">-- Select a pool --</option>\n                    {quiz.questionPools.map((pool) => (\n                      <option key={pool.id} value={pool.id}>\n                        {pool.title || `Pool ${pool.poolId}`} ({pool.questions.length} questions)\n                      </option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label htmlFor=\"newRuleSelectCount\" className=\"text-sm font-medium\">\n                    Number of Questions to Select\n                  </label>\n                  <input\n                    id=\"newRuleSelectCount\"\n                    type=\"number\"\n                    min=\"1\"\n                    value={newRuleSelectCount}\n                    onChange={(e) => setNewRuleSelectCount(e.target.value)}\n                    className=\"w-full p-2 border rounded-md\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={newRuleRandomize}\n                      onChange={(e) => setNewRuleRandomize(e.target.checked)}\n                    />\n                    <span>Randomize Selection</span>\n                  </label>\n                  <p className=\"text-xs text-muted-foreground ml-6\">\n                    If checked, questions will be randomly selected from the pool.\n                  </p>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={newRuleShuffleOrder}\n                      onChange={(e) => setNewRuleShuffleOrder(e.target.checked)}\n                    />\n                    <span>Shuffle Order</span>\n                  </label>\n                  <p className=\"text-xs text-muted-foreground ml-6\">\n                    If checked, the order of selected questions will be randomized.\n                  </p>\n                </div>\n                \n                <div className=\"flex justify-end\">\n                  <Button\n                    onClick={handleCreateRule}\n                    disabled={!newRulePoolId || !newRuleSelectCount || savingRule}\n                  >\n                    {savingRule ? \"Creating...\" : \"Create Rule\"}\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAsBe,SAAS,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAqB;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAInC;IACV,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,wBAAwB;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAMnC;IACV,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,4BAA4B;IAC5B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEjE,4CAA4C;IAC5C,MAAM,mBAAmB,OAAO;QAC9B,kBAAkB;QAClB,kBAAkB;QAElB,MAAM,OAAO,KAAK,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnD,IAAI,MAAM;YACR,eAAe;gBACb,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK,IAAI;gBACrB,aAAa,KAAK,WAAW,IAAI;YACnC;YAEA,+BAA+B;YAC/B,iBAAiB,KAAK,SAAS;YAE/B,kDAAkD;YAClD,MAAM,mBAAmB,KAAK,aAAa,CAAC,OAAO,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YAClF,sBAAsB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,iBAAiB,QAAQ,CAAC,EAAE,EAAE;QAClF;IACF;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,kBAAkB;QAElB,MAAM,OAAO,KAAK,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,MAAM;YACR,eAAe;gBACb,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,SAAS;gBACzB,cAAc,KAAK,YAAY;YACjC;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAmB;QACvB,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;oBACnB,OAAO;oBACP,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YAEnC,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,eAAe;2BAAI,KAAK,aAAa;wBAAE;4BAAE,GAAG,OAAO;4BAAE,WAAW,EAAE;wBAAC;qBAAE;gBACvE,CAAC;YAED,gBAAgB;YAChB,sBAAsB;YACtB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,0BAA0B;IAC1B,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa;QAElB,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE;gBAC9E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,YAAY,KAAK;oBACxB,aAAa,YAAY,WAAW;gBACtC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc,MAAM,SAAS,IAAI;YAEvC,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,CAAC,GAAG,CAAC,CAAA,OACpC,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;4BAAE,GAAG,IAAI;4BAAE,GAAG,WAAW;wBAAC,IAC1B;gBAER,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,gBAAgB;IAChB,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,6GAA6G;YACxH;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE;gBACtE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;oBAC7D,gBAAgB,KAAK,cAAc,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;gBACrE,CAAC;YAED,IAAI,mBAAmB,QAAQ;gBAC7B,kBAAkB;gBAClB,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,aAAa,SAAS;oBACtB,WAAW;oBACX,cAAc;gBAChB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YAEnC,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,gBAAgB;2BAAI,KAAK,cAAc;wBAAE;qBAAQ;gBACnD,CAAC;YAED,iBAAiB;YACjB,sBAAsB;YACtB,oBAAoB;YACpB,uBAAuB;YACvB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,0BAA0B;IAC1B,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa;QAElB,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE;gBAC9E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,YAAY,MAAM;oBAC1B,aAAa,YAAY,WAAW;oBACpC,WAAW,YAAY,SAAS;oBAChC,cAAc,YAAY,YAAY;gBACxC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc,MAAM,SAAS,IAAI;YAEvC,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,gBAAgB,KAAK,cAAc,CAAC,GAAG,CAAC,CAAA,OACtC,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;4BAAE,GAAG,IAAI;4BAAE,GAAG,WAAW;wBAAC,IAC1B;gBAER,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,gBAAgB;IAChB,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,yDAAyD;YACpE;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE;gBACtE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,gBAAgB,KAAK,cAAc,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACjE,CAAC;YAED,IAAI,mBAAmB,QAAQ;gBAC7B,kBAAkB;gBAClB,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,OAAO;YAAW,eAAe;;8BACrC,8OAAC,gIAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;sCAAQ;;;;;;sCAC3B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;sCAAQ;;;;;;sCAC3B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;sCAAS;;;;;;;;;;;;8BAG9B,8OAAC,gIAAA,CAAA,cAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAmB;4CAAQ,KAAK,aAAa,CAAC,MAAM;4CAAC;;;;;;;oCAElE,KAAK,aAAa,CAAC,MAAM,KAAK,kBAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,aAAa;0DAAW;;;;;;;;;;;6DAKjD,8OAAC;wCAAG,WAAU;kDACX,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;gDAEC,WAAW,CAAC,uDAAuD,EACjE,mBAAmB,KAAK,EAAE,GACtB,gCACA,2BACJ;gDACF,SAAS,IAAM,iBAAiB,KAAK,EAAE;0DAEvC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAe,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,KAAK,MAAM,EAAE;;;;;;8EAC/D,8OAAC;oEAAE,WAAU;;wEACV,KAAK,SAAS,CAAC,MAAM;wEAAC;;;;;;;;;;;;;sEAG3B,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,iBAAiB,KAAK,EAAE;4DAC1B;;8EAEA,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC;oEACC,OAAM;oEACN,WAAU;oEACV,MAAK;oEACL,SAAQ;oEACR,QAAO;8EAEP,cAAA,8OAAC;wEACC,eAAc;wEACd,gBAAe;wEACf,aAAa;wEACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;+CApCL,KAAK,EAAE;;;;;;;;;;;;;;;;0CA+CtB,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,4BACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAAsB;;;;;;8DAG3D,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,YAAY,KAAK;oDACxB,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACtE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAAsB;;;;;;8DAGjE,8OAAC;oDACC,IAAG;oDACH,OAAO,YAAY,WAAW;oDAC9B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC5E,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU;0DAET,aAAa,cAAc;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;;;;;;;;;;;yDAKrC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS/C,8OAAC,gIAAA,CAAA,cAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAmB;4CAAkB,KAAK,cAAc,CAAC,MAAM;4CAAC;;;;;;;oCAE7E,KAAK,cAAc,CAAC,MAAM,KAAK,kBAC9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS,IAAM,aAAa;0DAAW;;;;;;;;;;;6DAKjD,8OAAC;wCAAG,WAAU;kDACX,KAAK,cAAc,CAAC,GAAG,CAAC,CAAC;4CACxB,MAAM,OAAO,KAAK,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,MAAM;4CAE9D,qBACE,8OAAC;gDAEC,WAAW,CAAC,uDAAuD,EACjE,mBAAmB,KAAK,EAAE,GACtB,gCACA,2BACJ;gDACF,SAAS,IAAM,iBAAiB,KAAK,EAAE;0DAEvC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;;wEAAc;wEACjB,KAAK,WAAW;wEAAC;wEAAO,MAAM,SAAS,KAAK,MAAM;;;;;;;8EAE5D,8OAAC;oEAAE,WAAU;;wEACV,KAAK,SAAS,GAAG,qBAAqB;wEACtC,KAAK,YAAY,GAAG,qBAAqB;;;;;;;;;;;;;sEAG9C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,iBAAiB,KAAK,EAAE;4DAC1B;;8EAEA,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC;oEACC,OAAM;oEACN,WAAU;oEACV,MAAK;oEACL,SAAQ;oEACR,QAAO;8EAEP,cAAA,8OAAC;wEACC,eAAc;wEACd,gBAAe;wEACf,aAAa;wEACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;+CAvCL,KAAK,EAAE;;;;;wCA8ClB;;;;;;;;;;;;0CAKN,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,4BACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAsB;;;;;;8DAG1D,8OAAC;oDACC,IAAG;oDACH,OAAO,YAAY,MAAM;oDACzB,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACvE,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;gEAAqB,OAAO,KAAK,EAAE;;oEACjC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,KAAK,MAAM,EAAE;oEAAC;oEAAG,KAAK,SAAS,CAAC,MAAM;oEAAC;;+DADnD,KAAK,EAAE;;;;;;;;;;;;;;;;;sDAO1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAAsB;;;;;;8DAGjE,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,OAAO,YAAY,WAAW;oDAC9B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACtF,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,YAAY,SAAS;4DAC9B,UAAU,CAAC,IAAM,eAAe;oEAAC,GAAG,WAAW;oEAAE,WAAW,EAAE,MAAM,CAAC,OAAO;gEAAA;;;;;;sEAE9E,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;sDAKpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,YAAY,YAAY;4DACjC,UAAU,CAAC,IAAM,eAAe;oEAAC,GAAG,WAAW;oEAAE,cAAc,EAAE,MAAM,CAAC,OAAO;gEAAA;;;;;;sEAEjF,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;sDAKpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU;0DAET,aAAa,cAAc;;;;;;;;;;;;;;;;yDAKlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS/C,8OAAC,gIAAA,CAAA,cAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmB;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAe,WAAU;kEAAsB;;;;;;kEAG9D,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAqB,WAAU;kEAAsB;;;;;;kEAGpE,8OAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;wDACrD,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,CAAC,gBAAgB;8DAE1B,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmB;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAAsB;;;;;;kEAG7D,8OAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAChD,WAAU;wDACV,QAAQ;;0EAER,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;oEAAqB,OAAO,KAAK,EAAE;;wEACjC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,KAAK,MAAM,EAAE;wEAAC;wEAAG,KAAK,SAAS,CAAC,MAAM;wEAAC;;mEADnD,KAAK,EAAE;;;;;;;;;;;;;;;;;0DAO1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAqB,WAAU;kEAAsB;;;;;;kEAGpE,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,KAAI;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;wDACrD,WAAU;wDACV,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS;gEACT,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,OAAO;;;;;;0EAEvD,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;0DAKpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS;gEACT,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,OAAO;;;;;;0EAE1D,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;0DAKpD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,CAAC,iBAAiB,CAAC,sBAAsB;8DAElD,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD"}}, {"offset": {"line": 4421, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4427, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/PublishSettings.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Quiz, Question } from \"@/generated/prisma\";\nimport { Button } from \"@/components/ui/button\";\nimport Link from \"next/link\";\n\ninterface PublishSettingsProps {\n  quiz: Quiz & { questions: Question[] };\n  onPublishStatusChange: (isPublished: boolean) => void;\n  isSaving: boolean;\n}\n\nexport default function PublishSettings({\n  quiz,\n  onPublishStatusChange,\n  isSaving,\n}: PublishSettingsProps) {\n  const [showConfirmation, setShowConfirmation] = useState(false);\n\n  const handlePublishClick = () => {\n    if (quiz.isPublished) {\n      // If already published, unpublish immediately\n      onPublishStatusChange(false);\n    } else {\n      // If not published, show confirmation first\n      setShowConfirmation(true);\n    }\n  };\n\n  const handleConfirmPublish = () => {\n    onPublishStatusChange(true);\n    setShowConfirmation(false);\n  };\n\n  const handleCancelPublish = () => {\n    setShowConfirmation(false);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"p-4 border rounded-md bg-muted/30\">\n        <h3 className=\"font-medium mb-2\">Current Status</h3>\n        <div className=\"flex items-center space-x-2\">\n          <div\n            className={`w-3 h-3 rounded-full ${\n              quiz.isPublished ? \"bg-green-500\" : \"bg-amber-500\"\n            }`}\n          />\n          <p>\n            {quiz.isPublished\n              ? \"Published - This quiz is visible to others\"\n              : \"Draft - Only you can see this quiz\"}\n          </p>\n        </div>\n      </div>\n\n      {showConfirmation ? (\n        <div className=\"p-4 border rounded-md bg-primary/5\">\n          <h3 className=\"font-medium mb-2\">Publish Confirmation</h3>\n          <p className=\"mb-4\">\n            Are you sure you want to publish this quiz? Once published, it will be visible to others.\n          </p>\n          <div className=\"flex space-x-4\">\n            <Button onClick={handleConfirmPublish} disabled={isSaving}>\n              {isSaving ? \"Publishing...\" : \"Yes, Publish Quiz\"}\n            </Button>\n            <Button variant=\"outline\" onClick={handleCancelPublish} disabled={isSaving}>\n              Cancel\n            </Button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"flex space-x-4\">\n          <Button\n            onClick={handlePublishClick}\n            variant={quiz.isPublished ? \"outline\" : \"default\"}\n            disabled={isSaving}\n          >\n            {isSaving\n              ? quiz.isPublished\n                ? \"Unpublishing...\"\n                : \"Publishing...\"\n              : quiz.isPublished\n              ? \"Unpublish Quiz\"\n              : \"Publish Quiz\"}\n          </Button>\n\n          {quiz.isPublished && (\n            <Button asChild>\n              <Link href={`/quiz/${quiz.id}`} target=\"_blank\">\n                View Published Quiz\n              </Link>\n            </Button>\n          )}\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        <h3 className=\"font-medium\">Publishing Checklist</h3>\n        <ul className=\"space-y-2\">\n          <li className=\"flex items-start space-x-2\">\n            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.title ? \"bg-green-100 text-green-600\" : \"bg-amber-100 text-amber-600\"}`}>\n              {quiz.title ? \"✓\" : \"!\"}\n            </div>\n            <div>\n              <p className=\"font-medium\">Quiz Title</p>\n              <p className=\"text-sm text-muted-foreground\">\n                {quiz.title ? \"Title is set\" : \"Quiz needs a title\"}\n              </p>\n            </div>\n          </li>\n\n          <li className=\"flex items-start space-x-2\">\n            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.questions.length > 0 ? \"bg-green-100 text-green-600\" : \"bg-amber-100 text-amber-600\"}`}>\n              {quiz.questions.length > 0 ? \"✓\" : \"!\"}\n            </div>\n            <div>\n              <p className=\"font-medium\">Questions</p>\n              <p className=\"text-sm text-muted-foreground\">\n                {quiz.questions.length > 0\n                  ? `Quiz has ${quiz.questions.length} question(s)`\n                  : \"Quiz needs at least one question\"}\n              </p>\n            </div>\n          </li>\n\n          <li className=\"flex items-start space-x-2\">\n            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.passingScore !== null ? \"bg-green-100 text-green-600\" : \"bg-amber-100 text-amber-600\"}`}>\n              {quiz.passingScore !== null ? \"✓\" : \"!\"}\n            </div>\n            <div>\n              <p className=\"font-medium\">Passing Score</p>\n              <p className=\"text-sm text-muted-foreground\">\n                {quiz.passingScore !== null\n                  ? `Passing score is set to ${quiz.passingScore}%`\n                  : \"Consider setting a passing score\"}\n              </p>\n            </div>\n          </li>\n\n          <li className=\"flex items-start space-x-2\">\n            <div className={`mt-1 w-5 h-5 flex items-center justify-center rounded-full ${quiz.timeLimit !== null ? \"bg-green-100 text-green-600\" : \"bg-amber-100 text-amber-600\"}`}>\n              {quiz.timeLimit !== null ? \"✓\" : \"!\"}\n            </div>\n            <div>\n              <p className=\"font-medium\">Time Limit</p>\n              <p className=\"text-sm text-muted-foreground\">\n                {quiz.timeLimit !== null\n                  ? `Time limit is set to ${quiz.timeLimit} minutes`\n                  : \"Consider setting a time limit\"}\n              </p>\n            </div>\n          </li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAae,SAAS,gBAAgB,EACtC,IAAI,EACJ,qBAAqB,EACrB,QAAQ,EACa;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,qBAAqB;QACzB,IAAI,KAAK,WAAW,EAAE;YACpB,8CAA8C;YAC9C,sBAAsB;QACxB,OAAO;YACL,4CAA4C;YAC5C,oBAAoB;QACtB;IACF;IAEA,MAAM,uBAAuB;QAC3B,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,oBAAoB;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAW,CAAC,qBAAqB,EAC/B,KAAK,WAAW,GAAG,iBAAiB,gBACpC;;;;;;0CAEJ,8OAAC;0CACE,KAAK,WAAW,GACb,+CACA;;;;;;;;;;;;;;;;;;YAKT,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,8OAAC;wBAAE,WAAU;kCAAO;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAsB,UAAU;0CAC9C,WAAW,kBAAkB;;;;;;0CAEhC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAqB,UAAU;0CAAU;;;;;;;;;;;;;;;;;qCAMhF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAS,KAAK,WAAW,GAAG,YAAY;wBACxC,UAAU;kCAET,WACG,KAAK,WAAW,GACd,oBACA,kBACF,KAAK,WAAW,GAChB,mBACA;;;;;;oBAGL,KAAK,WAAW,kBACf,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4BAAE,QAAO;sCAAS;;;;;;;;;;;;;;;;;0BAQxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAc;;;;;;kCAC5B,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAW,CAAC,2DAA2D,EAAE,KAAK,KAAK,GAAG,gCAAgC,+BAA+B;kDACvJ,KAAK,KAAK,GAAG,MAAM;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DACV,KAAK,KAAK,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAW,CAAC,2DAA2D,EAAE,KAAK,SAAS,CAAC,MAAM,GAAG,IAAI,gCAAgC,+BAA+B;kDACtK,KAAK,SAAS,CAAC,MAAM,GAAG,IAAI,MAAM;;;;;;kDAErC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DACV,KAAK,SAAS,CAAC,MAAM,GAAG,IACrB,CAAC,SAAS,EAAE,KAAK,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,GAC/C;;;;;;;;;;;;;;;;;;0CAKV,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAW,CAAC,2DAA2D,EAAE,KAAK,YAAY,KAAK,OAAO,gCAAgC,+BAA+B;kDACvK,KAAK,YAAY,KAAK,OAAO,MAAM;;;;;;kDAEtC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DACV,KAAK,YAAY,KAAK,OACnB,CAAC,wBAAwB,EAAE,KAAK,YAAY,CAAC,CAAC,CAAC,GAC/C;;;;;;;;;;;;;;;;;;0CAKV,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAW,CAAC,2DAA2D,EAAE,KAAK,SAAS,KAAK,OAAO,gCAAgC,+BAA+B;kDACpK,KAAK,SAAS,KAAK,OAAO,MAAM;;;;;;kDAEnC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DACV,KAAK,SAAS,KAAK,OAChB,CAAC,qBAAqB,EAAE,KAAK,SAAS,CAAC,QAAQ,CAAC,GAChD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB"}}, {"offset": {"line": 4784, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/editor/QuizEditor.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { Quiz, Question, QuestionPool, SelectionRule } from \"@/generated/prisma\";\nimport { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport QuizDetailsForm from \"./QuizDetailsForm\";\nimport QuestionsManager from \"./QuestionsManager\";\nimport PoolsManager from \"./PoolsManager\";\nimport PublishSettings from \"./PublishSettings\";\n\ntype QuizWithRelations = Quiz & {\n  questions: Question[];\n  questionPools: (QuestionPool & {\n    questions: Question[];\n  })[];\n  selectionRules: SelectionRule[];\n};\n\ninterface QuizEditorProps {\n  quiz: QuizWithRelations;\n}\n\nexport default function QuizEditor({ quiz: initialQuiz }: QuizEditorProps) {\n  const router = useRouter();\n  const [quiz, setQuiz] = useState<QuizWithRelations>(initialQuiz);\n  const [activeTab, setActiveTab] = useState(\"details\");\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveMessage, setSaveMessage] = useState<string | null>(null);\n\n  // Save quiz details\n  const handleSaveQuizDetails = async (updatedDetails: Partial<Quiz>) => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updatedDetails),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update quiz details\");\n      }\n\n      const updatedQuiz = await response.json();\n      setQuiz((prev) => ({ ...prev, ...updatedQuiz }));\n      setSaveMessage(\"Quiz details saved successfully\");\n    } catch (error) {\n      console.error(\"Error saving quiz details:\", error);\n      setSaveMessage(\"Error saving quiz details\");\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Add a new question\n  const handleAddQuestion = async (newQuestion: Omit<Question, \"id\" | \"quizId\" | \"createdAt\" | \"updatedAt\">) => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/questions`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(newQuestion),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to add question\");\n      }\n\n      const addedQuestion = await response.json();\n      setQuiz((prev) => ({\n        ...prev,\n        questions: [...prev.questions, addedQuestion],\n      }));\n      setSaveMessage(\"Question added successfully\");\n    } catch (error) {\n      console.error(\"Error adding question:\", error);\n      setSaveMessage(\"Error adding question\");\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Update an existing question\n  const handleUpdateQuestion = async (questionId: string, updatedQuestion: Partial<Question>) => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/questions/${questionId}`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updatedQuestion),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to update question\");\n      }\n\n      const updatedQuestionData = await response.json();\n      setQuiz((prev) => ({\n        ...prev,\n        questions: prev.questions.map((q) =>\n          q.id === questionId ? { ...q, ...updatedQuestionData } : q\n        ),\n      }));\n      setSaveMessage(\"Question updated successfully\");\n    } catch (error) {\n      console.error(\"Error updating question:\", error);\n      setSaveMessage(\"Error updating question\");\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Delete a question\n  const handleDeleteQuestion = async (questionId: string) => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/questions/${questionId}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to delete question\");\n      }\n\n      setQuiz((prev) => ({\n        ...prev,\n        questions: prev.questions.filter((q) => q.id !== questionId),\n      }));\n      setSaveMessage(\"Question deleted successfully\");\n    } catch (error) {\n      console.error(\"Error deleting question:\", error);\n      setSaveMessage(\"Error deleting question\");\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Publish or unpublish the quiz\n  const handlePublishStatusChange = async (isPublished: boolean) => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      const response = await fetch(`/api/quizzes/${quiz.id}/publish`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ isPublished }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to ${isPublished ? \"publish\" : \"unpublish\"} quiz`);\n      }\n\n      setQuiz((prev) => ({\n        ...prev,\n        isPublished,\n      }));\n      setSaveMessage(`Quiz ${isPublished ? \"published\" : \"unpublished\"} successfully`);\n    } catch (error) {\n      console.error(`Error ${isPublished ? \"publishing\" : \"unpublishing\"} quiz:`, error);\n      setSaveMessage(`Error ${isPublished ? \"publishing\" : \"unpublishing\"} quiz`);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Clear save message after 3 seconds\n  useEffect(() => {\n    if (saveMessage) {\n      const timer = setTimeout(() => {\n        setSaveMessage(null);\n      }, 3000);\n      return () => clearTimeout(timer);\n    }\n  }, [saveMessage]);\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-3xl font-bold\">Edit Quiz</h1>\n        <div className=\"flex items-center gap-4\">\n          {saveMessage && (\n            <p className={`text-sm ${saveMessage.includes(\"Error\") ? \"text-red-500\" : \"text-green-500\"}`}>\n              {saveMessage}\n            </p>\n          )}\n          <Button\n            variant=\"outline\"\n            onClick={() => router.push(\"/dashboard/quizzes\")}\n          >\n            Back to Quizzes\n          </Button>\n          <Button\n            onClick={() => router.push(`/dashboard/quizzes/${quiz.id}/preview`)}\n          >\n            Preview Quiz\n          </Button>\n        </div>\n      </div>\n\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid grid-cols-4 w-full max-w-3xl\">\n          <TabsTrigger value=\"details\">Quiz Details</TabsTrigger>\n          <TabsTrigger value=\"questions\">Questions</TabsTrigger>\n          <TabsTrigger value=\"pools\">Question Pools</TabsTrigger>\n          <TabsTrigger value=\"publish\">Publish</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"details\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Quiz Details</CardTitle>\n              <CardDescription>\n                Edit the basic information for your quiz\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <QuizDetailsForm\n                quiz={quiz}\n                onSave={handleSaveQuizDetails}\n                isSaving={isSaving}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"questions\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Questions</CardTitle>\n              <CardDescription>\n                Add, edit, or remove questions from your quiz\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <QuestionsManager\n                questions={quiz.questions}\n                onAddQuestion={handleAddQuestion}\n                onUpdateQuestion={handleUpdateQuestion}\n                onDeleteQuestion={handleDeleteQuestion}\n                isSaving={isSaving}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"pools\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Question Pools</CardTitle>\n              <CardDescription>\n                Create pools of questions for dynamic selection\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <PoolsManager\n                quiz={quiz}\n                setQuiz={setQuiz}\n                isSaving={isSaving}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"publish\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Publish Settings</CardTitle>\n              <CardDescription>\n                Control the visibility and availability of your quiz\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <PublishSettings\n                quiz={quiz}\n                onPublishStatusChange={handlePublishStatusChange}\n                isSaving={isSaving}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAyBe,SAAS,WAAW,EAAE,MAAM,WAAW,EAAmB;IACvE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,oBAAoB;IACpB,MAAM,wBAAwB,OAAO;QACnC,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc,MAAM,SAAS,IAAI;YACvC,QAAQ,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,GAAG,WAAW;gBAAC,CAAC;YAC9C,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,eAAe;QACjB,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB,OAAO;QAC/B,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,gBAAgB,MAAM,SAAS,IAAI;YACzC,QAAQ,CAAC,OAAS,CAAC;oBACjB,GAAG,IAAI;oBACP,WAAW;2BAAI,KAAK,SAAS;wBAAE;qBAAc;gBAC/C,CAAC;YACD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,eAAe;QACjB,SAAU;YACR,YAAY;QACd;IACF;IAEA,8BAA8B;IAC9B,MAAM,uBAAuB,OAAO,YAAoB;QACtD,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE;gBAC9E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,sBAAsB,MAAM,SAAS,IAAI;YAC/C,QAAQ,CAAC,OAAS,CAAC;oBACjB,GAAG,IAAI;oBACP,WAAW,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,IAC7B,EAAE,EAAE,KAAK,aAAa;4BAAE,GAAG,CAAC;4BAAE,GAAG,mBAAmB;wBAAC,IAAI;gBAE7D,CAAC;YACD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,eAAe;QACjB,SAAU;YACR,YAAY;QACd;IACF;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,OAAO;QAClC,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE;gBAC9E,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,CAAC,OAAS,CAAC;oBACjB,GAAG,IAAI;oBACP,WAAW,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBACnD,CAAC;YACD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,eAAe;QACjB,SAAU;YACR,YAAY;QACd;IACF;IAEA,gCAAgC;IAChC,MAAM,4BAA4B,OAAO;QACvC,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAY;YACrC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,cAAc,YAAY,YAAY,KAAK,CAAC;YAC3E;YAEA,QAAQ,CAAC,OAAS,CAAC;oBACjB,GAAG,IAAI;oBACP;gBACF,CAAC;YACD,eAAe,CAAC,KAAK,EAAE,cAAc,cAAc,cAAc,aAAa,CAAC;QACjF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,cAAc,eAAe,eAAe,MAAM,CAAC,EAAE;YAC5E,eAAe,CAAC,MAAM,EAAE,cAAc,eAAe,eAAe,KAAK,CAAC;QAC5E,SAAU;YACR,YAAY;QACd;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,MAAM,QAAQ,WAAW;gBACvB,eAAe;YACjB,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC;gCAAE,WAAW,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,WAAW,iBAAiB,kBAAkB;0CACzF;;;;;;0CAGL,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,OAAO,IAAI,CAAC;0CAC5B;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;0CACnE;;;;;;;;;;;;;;;;;;0BAML,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAQ;;;;;;0CAC3B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;;;;;;;kCAG/B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,uJAAA,CAAA,UAAe;wCACd,MAAM;wCACN,QAAQ;wCACR,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,wJAAA,CAAA,UAAgB;wCACf,WAAW,KAAK,SAAS;wCACzB,eAAe;wCACf,kBAAkB;wCAClB,kBAAkB;wCAClB,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,oJAAA,CAAA,UAAY;wCACX,MAAM;wCACN,SAAS;wCACT,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,uJAAA,CAAA,UAAe;wCACd,MAAM;wCACN,uBAAuB;wCACvB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B"}}, {"offset": {"line": 5295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}