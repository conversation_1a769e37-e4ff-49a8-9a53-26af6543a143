{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/lib/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Combines class names using clsx and tailwind-merge\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Generates a random UUID\n */\nexport function generateUUID(): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\n/**\n * Formats a date to a readable string\n */\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Formats time in minutes to a readable string (e.g., \"1 hour 30 minutes\")\n */\nexport function formatTimeLimit(minutes: number): string {\n  if (minutes < 60) {\n    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;\n  }\n  \n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n  \n  if (remainingMinutes === 0) {\n    return `${hours} hour${hours !== 1 ? 's' : ''}`;\n  }\n  \n  return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;\n}\n\n/**\n * Truncates text to a specified length with ellipsis\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n/**\n * Debounces a function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null;\n  \n  return function(...args: Parameters<T>): void {\n    const later = () => {\n      timeout = null;\n      func(...args);\n    };\n    \n    if (timeout !== null) {\n      clearTimeout(timeout);\n    }\n    \n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Calculates the percentage of a value out of a total\n */\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n/**\n * Shuffles an array using Fisher-Yates algorithm\n */\nexport function shuffleArray<T>(array: T[]): T[] {\n  const newArray = [...array];\n  for (let i = newArray.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];\n  }\n  return newArray;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,SAAS;IACd,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,gBAAgB,OAAe;IAC7C,IAAI,UAAU,IAAI;QAChB,OAAO,GAAG,QAAQ,OAAO,EAAE,YAAY,IAAI,MAAM,IAAI;IACvD;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,qBAAqB,GAAG;QAC1B,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,IAAI;IACjD;IAEA,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,GAAG,CAAC,EAAE,iBAAiB,OAAO,EAAE,qBAAqB,IAAI,MAAM,IAAI;AAChH;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,SAAS,GAAG,IAAmB;QACpC,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;QACV;QAEA,IAAI,YAAY,MAAM;YACpB,aAAa;QACf;QAEA,UAAU,WAAW,OAAO;IAC9B;AACF;AAKO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAKO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT"}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/dashboard/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { signOut, useSession } from \"next-auth/react\";\nimport { Button } from \"@/components/ui/button\";\n\nexport default function Navbar() {\n  const pathname = usePathname();\n  const { data: session } = useSession();\n\n  const isActive = (path: string) => {\n    return pathname === path || pathname.startsWith(`${path}/`);\n  };\n\n  return (\n    <header className=\"border-b\">\n      <div className=\"container mx-auto px-4 flex h-16 items-center justify-between\">\n        <div className=\"flex items-center gap-6\">\n          <Link href=\"/\" className=\"text-xl font-bold\">\n            QuizFlow\n          </Link>\n          <nav className=\"hidden md:flex gap-6\">\n            <Link\n              href=\"/dashboard\"\n              className={`text-sm font-medium transition-colors hover:text-primary ${\n                isActive(\"/dashboard\") && !isActive(\"/dashboard/quizzes\") && !isActive(\"/dashboard/activity\")\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              }`}\n            >\n              Dashboard\n            </Link>\n            <Link\n              href=\"/dashboard/quizzes\"\n              className={`text-sm font-medium transition-colors hover:text-primary ${\n                isActive(\"/dashboard/quizzes\")\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              }`}\n            >\n              My Quizzes\n            </Link>\n            <Link\n              href=\"/dashboard/activity\"\n              className={`text-sm font-medium transition-colors hover:text-primary ${\n                isActive(\"/dashboard/activity\")\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              }`}\n            >\n              Activity\n            </Link>\n            <Link\n              href=\"/explore\"\n              className={`text-sm font-medium transition-colors hover:text-primary ${\n                isActive(\"/explore\")\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              }`}\n            >\n              Explore\n            </Link>\n            {session?.user?.role === 'admin' && (\n              <Link\n                href=\"/dashboard/admin\"\n                className={`text-sm font-medium transition-colors hover:text-primary ${\n                  isActive(\"/dashboard/admin\")\n                    ? \"text-primary\"\n                    : \"text-muted-foreground\"\n                }`}\n              >\n                Admin\n              </Link>\n            )}\n          </nav>\n        </div>\n        <div className=\"flex items-center gap-4\">\n          {session ? (\n            <div className=\"flex items-center gap-4\">\n              <div className=\"hidden md:block\">\n                <div className=\"text-sm font-medium\">{session.user.name}</div>\n                <div className=\"text-xs text-muted-foreground\">{session.user.email}</div>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => signOut({ callbackUrl: \"/\" })}\n              >\n                Sign Out\n              </Button>\n            </div>\n          ) : (\n            <Button asChild size=\"sm\">\n              <Link href=\"/auth/login\">Sign In</Link>\n            </Button>\n          )}\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa,QAAQ,SAAS,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC;IAC5D;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAoB;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAC,yDAAyD,EACnE,SAAS,iBAAiB,CAAC,SAAS,yBAAyB,CAAC,SAAS,yBACnE,iBACA,yBACJ;8CACH;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAC,yDAAyD,EACnE,SAAS,wBACL,iBACA,yBACJ;8CACH;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAC,yDAAyD,EACnE,SAAS,yBACL,iBACA,yBACJ;8CACH;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAC,yDAAyD,EACnE,SAAS,cACL,iBACA,yBACJ;8CACH;;;;;;gCAGA,SAAS,MAAM,SAAS,yBACvB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAC,yDAAyD,EACnE,SAAS,sBACL,iBACA,yBACJ;8CACH;;;;;;;;;;;;;;;;;;8BAMP,8OAAC;oBAAI,WAAU;8BACZ,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAuB,QAAQ,IAAI,CAAC,IAAI;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAiC,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;0CAEpE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;wCAAE,aAAa;oCAAI;0CAC3C;;;;;;;;;;;6CAKH,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;kCACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC"}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}