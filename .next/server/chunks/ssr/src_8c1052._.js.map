{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/app/dashboard/quizzes/create/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useSession } from \"next-auth/react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { generateUUID } from \"@/lib/utils\";\n\nexport default function CreateQuizPage() {\n  const router = useRouter();\n  const { data: session } = useSession();\n  const [title, setTitle] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [tags, setTags] = useState(\"\");\n  const [passingScore, setPassingScore] = useState(70);\n  const [timeLimit, setTimeLimit] = useState(15);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError(null);\n\n    if (!session) {\n      setError(\"You must be logged in to create a quiz\");\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      const quizData = {\n        quizId: generateUUID(),\n        title,\n        description,\n        tags: tags.split(\",\").map(tag => tag.trim()).filter(tag => tag),\n        passingScore: parseFloat(passingScore.toString()),\n        timeLimit: parseInt(timeLimit.toString()),\n      };\n\n      const response = await fetch(\"/api/quizzes\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(quizData),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || \"Failed to create quiz\");\n      }\n\n      router.push(`/dashboard/quizzes/${data.id}/edit`);\n    } catch (error) {\n      if (error instanceof Error) {\n        setError(error.message);\n      } else {\n        setError(\"An error occurred while creating the quiz\");\n      }\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto py-8 px-4\">\n      <div className=\"max-w-3xl mx-auto\">\n        <h1 className=\"text-3xl font-bold mb-8\">Create New Quiz</h1>\n        \n        <Card>\n          <CardHeader>\n            <CardTitle>Quiz Details</CardTitle>\n            <CardDescription>\n              Enter the basic information for your new quiz\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {error && (\n                <div className=\"p-3 text-sm bg-red-50 text-red-500 rounded-md\">\n                  {error}\n                </div>\n              )}\n              \n              <div className=\"space-y-2\">\n                <label htmlFor=\"title\" className=\"text-sm font-medium\">\n                  Quiz Title\n                </label>\n                <input\n                  id=\"title\"\n                  type=\"text\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  className=\"w-full p-2 border rounded-md\"\n                  placeholder=\"Enter quiz title\"\n                  required\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label htmlFor=\"description\" className=\"text-sm font-medium\">\n                  Description\n                </label>\n                <textarea\n                  id=\"description\"\n                  value={description}\n                  onChange={(e) => setDescription(e.target.value)}\n                  className=\"w-full p-2 border rounded-md min-h-[100px]\"\n                  placeholder=\"Enter quiz description\"\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label htmlFor=\"tags\" className=\"text-sm font-medium\">\n                  Tags\n                </label>\n                <input\n                  id=\"tags\"\n                  type=\"text\"\n                  value={tags}\n                  onChange={(e) => setTags(e.target.value)}\n                  className=\"w-full p-2 border rounded-md\"\n                  placeholder=\"Enter tags separated by commas (e.g., security, basics, networking)\"\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"passingScore\" className=\"text-sm font-medium\">\n                    Passing Score (%)\n                  </label>\n                  <input\n                    id=\"passingScore\"\n                    type=\"number\"\n                    min=\"0\"\n                    max=\"100\"\n                    value={passingScore}\n                    onChange={(e) => setPassingScore(parseInt(e.target.value))}\n                    className=\"w-full p-2 border rounded-md\"\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label htmlFor=\"timeLimit\" className=\"text-sm font-medium\">\n                    Time Limit (minutes)\n                  </label>\n                  <input\n                    id=\"timeLimit\"\n                    type=\"number\"\n                    min=\"1\"\n                    value={timeLimit}\n                    onChange={(e) => setTimeLimit(parseInt(e.target.value))}\n                    className=\"w-full p-2 border rounded-md\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"flex justify-end space-x-4 pt-4\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => router.back()}\n                  disabled={isLoading}\n                >\n                  Cancel\n                </Button>\n                <Button type=\"submit\" disabled={isLoading}>\n                  {isLoading ? \"Creating...\" : \"Create Quiz\"}\n                </Button>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI,CAAC,SAAS;YACZ,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI;YACF,MAAM,WAAW;gBACf,QAAQ,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;gBACnB;gBACA;gBACA,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO;gBAC3D,cAAc,WAAW,aAAa,QAAQ;gBAC9C,WAAW,SAAS,UAAU,QAAQ;YACxC;YAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;QAClD,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,SAAS;YACX;YACA,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;oCACrC,uBACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAsB;;;;;;0DAGvD,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAAsB;;;;;;0DAG7D,8OAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAAsB;;;;;;0DAGtD,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACvC,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAe,WAAU;kEAAsB;;;;;;kEAG9D,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxD,WAAU;;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAY,WAAU;kEAAsB;;;;;;kEAG3D,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,KAAI;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;wDACrD,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,OAAO,IAAI;gDAC1B,UAAU;0DACX;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,UAAU;0DAC7B,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C"}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}