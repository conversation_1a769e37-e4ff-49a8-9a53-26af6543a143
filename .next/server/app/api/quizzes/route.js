const CHUNK_PUBLIC_PATH = "server/app/api/quizzes/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root of the server]__0a8410._.js");
runtime.loadChunk("server/chunks/node_modules_next_997bc5._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_d209f9._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a801._.js");
runtime.loadChunk("server/chunks/node_modules_a242f1._.js");
runtime.loadChunk("server/chunks/_535470._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/quizzes/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/quizzes/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
