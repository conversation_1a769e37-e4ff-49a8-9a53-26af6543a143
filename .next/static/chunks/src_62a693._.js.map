{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/lib/utils/qfjson-parser.ts"], "sourcesContent": ["/**\n * QuizFlow JSON Parser\n *\n * This module provides utilities for parsing and validating QuizFlow JSON (QFJSON) files.\n */\n\nimport { z } from 'zod';\nimport {\n  QuizFlowJSON,\n  Question,\n  QuestionPool,\n  SelectionRule,\n  TextContent\n} from '@/types/qfjson';\n\n// Helper function to get text content based on locale\nexport function getLocalizedText(text: TextContent | undefined | null, locale?: string): string {\n  // Handle null/undefined cases\n  if (!text) {\n    return '';\n  }\n\n  if (typeof text === 'string') {\n    return text;\n  }\n\n  // Ensure text is an object before accessing properties\n  if (typeof text !== 'object') {\n    return String(text);\n  }\n\n  // If locale is provided, try to get that specific translation\n  if (locale && text[locale]) {\n    return text[locale];\n  }\n\n  // Fall back to default\n  return text.default || '';\n}\n\n// Basic validation schema for QFJSON\nconst mediaItemSchema = z.object({\n  type: z.enum(['image', 'audio', 'video']),\n  url: z.string().url(),\n  alt_text: z.string().optional(),\n  caption: z.string().optional(),\n});\n\nconst hintItemSchema = z.object({\n  text: z.string(),\n  delay_seconds: z.number().optional(),\n});\n\nconst dependencySchema = z.object({\n  question_id: z.string(),\n  condition: z.enum([\n    'correct',\n    'incorrect',\n    'answered',\n    'not_answered',\n    'answer_is',\n    'selected_option_id'\n  ]),\n  value: z.union([z.string(), z.array(z.string())]).optional(),\n});\n\nconst baseQuestionSchema = z.object({\n  question_id: z.string(),\n  type: z.string(),\n  text: z.union([z.string(), z.record(z.string())]),\n  points: z.number().positive(),\n  feedback_correct: z.string().optional(),\n  feedback_incorrect: z.string().optional(),\n  media: z.array(mediaItemSchema).optional(),\n  hint: z.array(hintItemSchema).optional(),\n  depends_on: dependencySchema.optional(),\n});\n\nconst metadataSchema = z.object({\n  format_version: z.string(),\n  quiz_id: z.string(),\n  title: z.union([z.string(), z.record(z.string())]),\n  description: z.union([z.string(), z.record(z.string())]).optional(),\n  author: z.string().optional(),\n  creation_date: z.string().optional(), // ISO 8601 format\n  tags: z.array(z.string()).optional(),\n  passing_score_percentage: z.number().min(0).max(100).optional(),\n  time_limit_minutes: z.number().positive().optional(),\n  markup_format: z.enum(['markdown', 'html', 'plain_text']).optional(),\n  locale: z.string().optional(),\n});\n\nconst quizFlowSchema = z.object({\n  quiz: z.object({\n    $schema: z.string().optional(),\n    metadata: metadataSchema,\n    questions: z.array(baseQuestionSchema).optional(),\n    question_pools: z.array(\n      z.object({\n        pool_id: z.string(),\n        title: z.string().optional(),\n        description: z.string().optional(),\n        questions: z.array(baseQuestionSchema),\n      })\n    ).optional(),\n    selection_rules: z.array(\n      z.object({\n        pool_id: z.string(),\n        select_count: z.number().positive(),\n        randomize: z.boolean().optional(),\n        shuffle_order: z.boolean().optional(),\n      })\n    ).optional(),\n  }),\n});\n\n/**\n * Validates a QuizFlow JSON object against the basic schema\n * @param data The QuizFlow JSON object to validate\n * @returns A tuple with [isValid, errors]\n */\nexport function validateQuizFlowJSON(data: any): [boolean, any] {\n  try {\n    quizFlowSchema.parse(data);\n    return [true, null];\n  } catch (error) {\n    return [false, error];\n  }\n}\n\n/**\n * Parses a QuizFlow JSON string into a strongly-typed object\n * @param jsonString The QuizFlow JSON string to parse\n * @returns The parsed QuizFlow JSON object\n */\nexport function parseQuizFlowJSON(jsonString: string): QuizFlowJSON {\n  try {\n    const data = JSON.parse(jsonString);\n    const [isValid, errors] = validateQuizFlowJSON(data);\n\n    if (!isValid) {\n      throw new Error(`Invalid QuizFlow JSON: ${JSON.stringify(errors)}`);\n    }\n\n    return data as QuizFlowJSON;\n  } catch (error) {\n    throw new Error(`Failed to parse QuizFlow JSON: ${error}`);\n  }\n}\n\n/**\n * Assembles the final list of questions for a quiz based on question pools and selection rules\n * @param quiz The QuizFlow JSON object\n * @returns An array of questions to be presented in the quiz\n */\nexport function assembleQuizQuestions(quiz: QuizFlowJSON): Question[] {\n  // If there are no question pools or selection rules, return the questions array directly\n  if (!quiz.quiz.question_pools || !quiz.quiz.selection_rules || quiz.quiz.question_pools.length === 0) {\n    return quiz.quiz.questions || [];\n  }\n\n  const selectedQuestions: Question[] = [];\n\n  // Process each selection rule\n  quiz.quiz.selection_rules.forEach((rule: SelectionRule) => {\n    // Find the corresponding question pool\n    const pool = quiz.quiz.question_pools?.find(p => p.pool_id === rule.pool_id);\n\n    if (pool && pool.questions.length > 0) {\n      let poolQuestions = [...pool.questions];\n\n      // Randomize if specified\n      if (rule.randomize) {\n        poolQuestions = shuffleArray(poolQuestions);\n      }\n\n      // Select the specified number of questions (or all if select_count is greater)\n      const count = Math.min(rule.select_count, poolQuestions.length);\n      const selected = poolQuestions.slice(0, count);\n\n      // Shuffle the order of selected questions if specified\n      if (rule.shuffle_order) {\n        shuffleArray(selected);\n      }\n\n      selectedQuestions.push(...selected);\n    }\n  });\n\n  // Add any questions directly in the quiz.questions array\n  if (quiz.quiz.questions && quiz.quiz.questions.length > 0) {\n    selectedQuestions.push(...quiz.quiz.questions);\n  }\n\n  return selectedQuestions;\n}\n\n// Helper function to shuffle an array\nfunction shuffleArray<T>(array: T[]): T[] {\n  const newArray = [...array];\n  for (let i = newArray.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];\n  }\n  return newArray;\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;AAED;AAAA;;AAUO,SAAS,iBAAiB,IAAoC,EAAE,MAAe;IACpF,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,uDAAuD;IACvD,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,OAAO;IAChB;IAEA,8DAA8D;IAC9D,IAAI,UAAU,IAAI,CAAC,OAAO,EAAE;QAC1B,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,uBAAuB;IACvB,OAAO,KAAK,OAAO,IAAI;AACzB;AAEA,qCAAqC;AACrC,MAAM,kBAAkB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAS;KAAQ;IACxC,KAAK,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IACnB,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAEA,MAAM,iBAAiB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM;IACd,eAAe,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACpC;AAEA,MAAM,mBAAmB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,WAAW,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAChB;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;KAAI,EAAE,QAAQ;AAC5D;AAEA,MAAM,qBAAqB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM;IACd,MAAM,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;KAAI;IAChD,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,kBAAkB,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,oBAAoB,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,OAAO,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iBAAiB,QAAQ;IACxC,MAAM,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,gBAAgB,QAAQ;IACtC,YAAY,iBAAiB,QAAQ;AACvC;AAEA,MAAM,iBAAiB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,gBAAgB,oLAAA,CAAA,IAAC,CAAC,MAAM;IACxB,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM;IACjB,OAAO,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;KAAI;IACjD,aAAa,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,oLAAA,CAAA,IAAC,CAAC,MAAM;KAAI,EAAE,QAAQ;IACjE,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,eAAe,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,MAAM,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAClC,0BAA0B,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ;IAC7D,oBAAoB,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClD,eAAe,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAQ;KAAa,EAAE,QAAQ;IAClE,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B;AAEA,MAAM,iBAAiB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACb,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,UAAU;QACV,WAAW,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oBAAoB,QAAQ;QAC/C,gBAAgB,oLAAA,CAAA,IAAC,CAAC,KAAK,CACrB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM;YACjB,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC1B,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,WAAW,oLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QACrB,IACA,QAAQ;QACV,iBAAiB,oLAAA,CAAA,IAAC,CAAC,KAAK,CACtB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM;YACjB,cAAc,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACjC,WAAW,oLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YAC/B,eAAe,oLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QACrC,IACA,QAAQ;IACZ;AACF;AAOO,SAAS,qBAAqB,IAAS;IAC5C,IAAI;QACF,eAAe,KAAK,CAAC;QACrB,OAAO;YAAC;YAAM;SAAK;IACrB,EAAE,OAAO,OAAO;QACd,OAAO;YAAC;YAAO;SAAM;IACvB;AACF;AAOO,SAAS,kBAAkB,UAAkB;IAClD,IAAI;QACF,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,MAAM,CAAC,SAAS,OAAO,GAAG,qBAAqB;QAE/C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,KAAK,SAAS,CAAC,SAAS;QACpE;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,OAAO;IAC3D;AACF;AAOO,SAAS,sBAAsB,IAAkB;IACtD,yFAAyF;IACzF,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,GAAG;QACpG,OAAO,KAAK,IAAI,CAAC,SAAS,IAAI,EAAE;IAClC;IAEA,MAAM,oBAAgC,EAAE;IAExC,8BAA8B;IAC9B,KAAK,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACjC,uCAAuC;QACvC,MAAM,OAAO,KAAK,IAAI,CAAC,cAAc,EAAE,KAAK,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,OAAO;QAE3E,IAAI,QAAQ,KAAK,SAAS,CAAC,MAAM,GAAG,GAAG;YACrC,IAAI,gBAAgB;mBAAI,KAAK,SAAS;aAAC;YAEvC,yBAAyB;YACzB,IAAI,KAAK,SAAS,EAAE;gBAClB,gBAAgB,aAAa;YAC/B;YAEA,+EAA+E;YAC/E,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,YAAY,EAAE,cAAc,MAAM;YAC9D,MAAM,WAAW,cAAc,KAAK,CAAC,GAAG;YAExC,uDAAuD;YACvD,IAAI,KAAK,aAAa,EAAE;gBACtB,aAAa;YACf;YAEA,kBAAkB,IAAI,IAAI;QAC5B;IACF;IAEA,yDAAyD;IACzD,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG;QACzD,kBAAkB,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS;IAC/C;IAEA,OAAO;AACT;AAEA,sCAAsC;AACtC,SAAS,aAAgB,KAAU;IACjC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT"}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/lib/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Combines class names using clsx and tailwind-merge\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Generates a random UUID\n */\nexport function generateUUID(): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n}\n\n/**\n * Formats a date to a readable string\n */\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Formats time in minutes to a readable string (e.g., \"1 hour 30 minutes\")\n */\nexport function formatTimeLimit(minutes: number): string {\n  if (minutes < 60) {\n    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;\n  }\n  \n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n  \n  if (remainingMinutes === 0) {\n    return `${hours} hour${hours !== 1 ? 's' : ''}`;\n  }\n  \n  return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;\n}\n\n/**\n * Truncates text to a specified length with ellipsis\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n/**\n * Debounces a function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null;\n  \n  return function(...args: Parameters<T>): void {\n    const later = () => {\n      timeout = null;\n      func(...args);\n    };\n    \n    if (timeout !== null) {\n      clearTimeout(timeout);\n    }\n    \n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Calculates the percentage of a value out of a total\n */\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n/**\n * Shuffles an array using Fisher-Yates algorithm\n */\nexport function shuffleArray<T>(array: T[]): T[] {\n  const newArray = [...array];\n  for (let i = newArray.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];\n  }\n  return newArray;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,SAAS;IACd,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,gBAAgB,OAAe;IAC7C,IAAI,UAAU,IAAI;QAChB,OAAO,GAAG,QAAQ,OAAO,EAAE,YAAY,IAAI,MAAM,IAAI;IACvD;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,qBAAqB,GAAG;QAC1B,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,IAAI;IACjD;IAEA,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,GAAG,CAAC,EAAE,iBAAiB,OAAO,EAAE,qBAAqB,IAAI,MAAM,IAAI;AAChH;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,SAAS,GAAG,IAAmB;QACpC,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;QACV;QAEA,IAAI,YAAY,MAAM;YACpB,aAAa;QACf;QAEA,UAAU,WAAW,OAAO;IAC9B;AACF;AAKO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAKO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT"}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/question-types/MultipleChoiceQuestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { MultipleChoiceQuestion as MultipleChoiceQuestionType } from \"@/types/qfjson\";\nimport { getLocalizedText } from \"@/lib/utils/qfjson-parser\";\nimport { cn } from \"@/lib/utils\";\n\ninterface MultipleChoiceQuestionProps {\n  question: MultipleChoiceQuestionType;\n  answer: string | string[];\n  onAnswerChange: (answer: string | string[]) => void;\n  locale?: string;\n  disabled?: boolean;\n}\n\nconst MultipleChoiceQuestion: React.FC<MultipleChoiceQuestionProps> = ({\n  question,\n  answer,\n  onAnswerChange,\n  locale = \"en-US\",\n  disabled = false,\n}) => {\n  const handleSingleChoiceChange = (optionId: string) => {\n    onAnswerChange(optionId);\n  };\n\n  const handleMultipleChoiceChange = (optionId: string) => {\n    const currentAnswers = Array.isArray(answer) ? answer : answer ? [answer] : [];\n    \n    if (currentAnswers.includes(optionId)) {\n      // Remove the option if already selected\n      onAnswerChange(currentAnswers.filter(id => id !== optionId));\n    } else {\n      // Add the option if not already selected\n      onAnswerChange([...currentAnswers, optionId]);\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"space-y-2\">\n        {question.options.map((option) => {\n          const isSelected = Array.isArray(answer) \n            ? answer.includes(option.id) \n            : answer === option.id;\n          \n          return (\n            <div\n              key={option.id}\n              className={cn(\n                \"flex items-start space-x-3 p-3 border rounded-md cursor-pointer transition-colors\",\n                isSelected \n                  ? \"border-primary bg-primary/5\" \n                  : \"border-input hover:border-primary/50\",\n                disabled && \"opacity-60 cursor-not-allowed\"\n              )}\n              onClick={() => {\n                if (disabled) return;\n                question.single_correct_answer \n                  ? handleSingleChoiceChange(option.id) \n                  : handleMultipleChoiceChange(option.id);\n              }}\n            >\n              <div className=\"flex-shrink-0 mt-0.5\">\n                {question.single_correct_answer ? (\n                  <div className={cn(\n                    \"w-5 h-5 border-2 rounded-full flex items-center justify-center\",\n                    isSelected ? \"border-primary\" : \"border-muted-foreground\"\n                  )}>\n                    {isSelected && (\n                      <div className=\"w-3 h-3 rounded-full bg-primary\" />\n                    )}\n                  </div>\n                ) : (\n                  <div className={cn(\n                    \"w-5 h-5 border-2 rounded-sm flex items-center justify-center\",\n                    isSelected ? \"border-primary bg-primary\" : \"border-muted-foreground\"\n                  )}>\n                    {isSelected && (\n                      <svg \n                        xmlns=\"http://www.w3.org/2000/svg\" \n                        viewBox=\"0 0 24 24\" \n                        fill=\"none\" \n                        stroke=\"currentColor\" \n                        strokeWidth=\"3\" \n                        strokeLinecap=\"round\" \n                        strokeLinejoin=\"round\"\n                        className=\"w-3 h-3 text-primary-foreground\"\n                      >\n                        <polyline points=\"20 6 9 17 4 12\" />\n                      </svg>\n                    )}\n                  </div>\n                )}\n              </div>\n              <div className=\"flex-1\">\n                <div className=\"text-sm font-medium\">\n                  {getLocalizedText(option.text, locale)}\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default MultipleChoiceQuestion;\n"], "names": [], "mappings": ";;;;AAIA;AACA;AALA;;;;AAeA,MAAM,yBAAgE,CAAC,EACrE,QAAQ,EACR,MAAM,EACN,cAAc,EACd,SAAS,OAAO,EAChB,WAAW,KAAK,EACjB;IACC,MAAM,2BAA2B,CAAC;QAChC,eAAe;IACjB;IAEA,MAAM,6BAA6B,CAAC;QAClC,MAAM,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,SAAS;YAAC;SAAO,GAAG,EAAE;QAE9E,IAAI,eAAe,QAAQ,CAAC,WAAW;YACrC,wCAAwC;YACxC,eAAe,eAAe,MAAM,CAAC,CAAA,KAAM,OAAO;QACpD,OAAO;YACL,yCAAyC;YACzC,eAAe;mBAAI;gBAAgB;aAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC;gBACrB,MAAM,aAAa,MAAM,OAAO,CAAC,UAC7B,OAAO,QAAQ,CAAC,OAAO,EAAE,IACzB,WAAW,OAAO,EAAE;gBAExB,qBACE,6LAAC;oBAEC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,qFACA,aACI,gCACA,wCACJ,YAAY;oBAEd,SAAS;wBACP,IAAI,UAAU;wBACd,SAAS,qBAAqB,GAC1B,yBAAyB,OAAO,EAAE,IAClC,2BAA2B,OAAO,EAAE;oBAC1C;;sCAEA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,qBAAqB,iBAC7B,6LAAC;gCAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACf,kEACA,aAAa,mBAAmB;0CAE/B,4BACC,6LAAC;oCAAI,WAAU;;;;;;;;;;qDAInB,6LAAC;gCAAI,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACf,gEACA,aAAa,8BAA8B;0CAE1C,4BACC,6LAAC;oCACC,OAAM;oCACN,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;8CAEV,cAAA,6LAAC;wCAAS,QAAO;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,IAAI,EAAE;;;;;;;;;;;;mBAjD9B,OAAO,EAAE;;;;;YAsDpB;;;;;;;;;;;AAIR;KA3FM;uCA6FS"}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/question-types/TrueFalseQuestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { TrueFalseQuestion as TrueFalseQuestionType } from \"@/types/qfjson\";\nimport { cn } from \"@/lib/utils\";\n\ninterface TrueFalseQuestionProps {\n  question: TrueFalseQuestionType;\n  answer: boolean | null;\n  onAnswerChange: (answer: boolean) => void;\n  disabled?: boolean;\n}\n\nconst TrueFalseQuestion: React.FC<TrueFalseQuestionProps> = ({\n  question,\n  answer,\n  onAnswerChange,\n  disabled = false,\n}) => {\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3\">\n        <div\n          className={cn(\n            \"flex-1 p-4 border rounded-md cursor-pointer transition-colors text-center\",\n            answer === true \n              ? \"border-primary bg-primary/5\" \n              : \"border-input hover:border-primary/50\",\n            disabled && \"opacity-60 cursor-not-allowed\"\n          )}\n          onClick={() => {\n            if (!disabled) onAnswerChange(true);\n          }}\n        >\n          <div className=\"font-medium\">True</div>\n        </div>\n        \n        <div\n          className={cn(\n            \"flex-1 p-4 border rounded-md cursor-pointer transition-colors text-center\",\n            answer === false \n              ? \"border-primary bg-primary/5\" \n              : \"border-input hover:border-primary/50\",\n            disabled && \"opacity-60 cursor-not-allowed\"\n          )}\n          onClick={() => {\n            if (!disabled) onAnswerChange(false);\n          }}\n        >\n          <div className=\"font-medium\">False</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrueFalseQuestion;\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAaA,MAAM,oBAAsD,CAAC,EAC3D,QAAQ,EACR,MAAM,EACN,cAAc,EACd,WAAW,KAAK,EACjB;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,6EACA,WAAW,OACP,gCACA,wCACJ,YAAY;oBAEd,SAAS;wBACP,IAAI,CAAC,UAAU,eAAe;oBAChC;8BAEA,cAAA,6LAAC;wBAAI,WAAU;kCAAc;;;;;;;;;;;8BAG/B,6LAAC;oBACC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,6EACA,WAAW,QACP,gCACA,wCACJ,YAAY;oBAEd,SAAS;wBACP,IAAI,CAAC,UAAU,eAAe;oBAChC;8BAEA,cAAA,6LAAC;wBAAI,WAAU;kCAAc;;;;;;;;;;;;;;;;;;;;;;AAKvC;KAzCM;uCA2CS"}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/question-types/ShortAnswerQuestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { ShortAnswerQuestion as ShortAnswerQuestionType } from \"@/types/qfjson\";\n\ninterface ShortAnswerQuestionProps {\n  question: ShortAnswerQuestionType;\n  answer: string;\n  onAnswerChange: (answer: string) => void;\n  disabled?: boolean;\n}\n\nconst ShortAnswerQuestion: React.FC<ShortAnswerQuestionProps> = ({\n  question,\n  answer,\n  onAnswerChange,\n  disabled = false,\n}) => {\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"relative\">\n        <input\n          type=\"text\"\n          value={answer || \"\"}\n          onChange={(e) => onAnswerChange(e.target.value)}\n          disabled={disabled}\n          placeholder=\"Type your answer here...\"\n          className=\"w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed\"\n        />\n      </div>\n      \n      {question.exact_match === false && (\n        <p className=\"text-sm text-muted-foreground\">\n          Note: Your answer will be evaluated based on keywords, not exact matching.\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default ShortAnswerQuestion;\n"], "names": [], "mappings": ";;;;AAAA;;AAYA,MAAM,sBAA0D,CAAC,EAC/D,QAAQ,EACR,MAAM,EACN,cAAc,EACd,WAAW,KAAK,EACjB;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,MAAK;oBACL,OAAO,UAAU;oBACjB,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oBAC9C,UAAU;oBACV,aAAY;oBACZ,WAAU;;;;;;;;;;;YAIb,SAAS,WAAW,KAAK,uBACxB,6LAAC;gBAAE,WAAU;0BAAgC;;;;;;;;;;;;AAMrD;KA1BM;uCA4BS"}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/question-types/MatchingQuestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { MatchingQuestion as MatchingQuestionType } from \"@/types/qfjson\";\nimport { getLocalizedText } from \"@/lib/utils/qfjson-parser\";\n\ninterface MatchingQuestionProps {\n  question: MatchingQuestionType;\n  answer: Record<string, string>; // Map of stem_id to option_id\n  onAnswerChange: (answer: Record<string, string>) => void;\n  locale?: string;\n  disabled?: boolean;\n}\n\nconst MatchingQuestion: React.FC<MatchingQuestionProps> = ({\n  question,\n  answer = {},\n  onAnswerChange,\n  locale = \"en-US\",\n  disabled = false,\n}) => {\n  const handleSelectChange = (stemId: string, optionId: string) => {\n    onAnswerChange({\n      ...answer,\n      [stemId]: optionId\n    });\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {question.stems.map((stem) => (\n        <div key={stem.id} className=\"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4\">\n          <div className=\"sm:w-1/2 font-medium\">\n            {getLocalizedText(stem.text, locale)}\n          </div>\n          <div className=\"sm:w-1/2\">\n            <select\n              value={answer[stem.id] || \"\"}\n              onChange={(e) => handleSelectChange(stem.id, e.target.value)}\n              disabled={disabled}\n              className=\"w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed\"\n            >\n              <option value=\"\">-- Select an option --</option>\n              {question.options.map((option) => (\n                <option key={option.id} value={option.id}>\n                  {getLocalizedText(option.text, locale)}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default MatchingQuestion;\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAcA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,cAAc,EACd,SAAS,OAAO,EAChB,WAAW,KAAK,EACjB;IACC,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,eAAe;YACb,GAAG,MAAM;YACT,CAAC,OAAO,EAAE;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,qBACnB,6LAAC;gBAAkB,WAAU;;kCAC3B,6LAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI,EAAE;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI;4BAC1B,UAAU,CAAC,IAAM,mBAAmB,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC3D,UAAU;4BACV,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;gCAChB,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,uBACrB,6LAAC;wCAAuB,OAAO,OAAO,EAAE;kDACrC,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,IAAI,EAAE;uCADpB,OAAO,EAAE;;;;;;;;;;;;;;;;;eAbpB,KAAK,EAAE;;;;;;;;;;AAuBzB;KAxCM;uCA0CS"}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/question-types/FillInTheBlankQuestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { FillInTheBlankQuestion as FillInTheBlankQuestionType } from \"@/types/qfjson\";\nimport { getLocalizedText } from \"@/lib/utils/qfjson-parser\";\n\ninterface FillInTheBlankQuestionProps {\n  question: FillInTheBlankQuestionType;\n  answer: Record<string, string>; // Map of blank_id to user's answer\n  onAnswerChange: (answer: Record<string, string>) => void;\n  locale?: string;\n  disabled?: boolean;\n}\n\nconst FillInTheBlankQuestion: React.FC<FillInTheBlankQuestionProps> = ({\n  question,\n  answer = {},\n  onAnswerChange,\n  locale = \"en-US\",\n  disabled = false,\n}) => {\n  const handleBlankChange = (blankId: string, value: string) => {\n    onAnswerChange({\n      ...answer,\n      [blankId]: value\n    });\n  };\n\n  // Parse the text template and replace [BLANK] with input fields\n  const renderTemplate = () => {\n    const template = getLocalizedText(question.text_template, locale);\n    const parts = template.split(/(\\[BLANK\\])/g);\n    \n    let blankIndex = 0;\n    \n    return (\n      <div className=\"space-y-4\">\n        {parts.map((part, index) => {\n          if (part === \"[BLANK]\") {\n            const blank = question.blanks[blankIndex];\n            const currentBlankIndex = blankIndex;\n            blankIndex++;\n            \n            return (\n              <input\n                key={index}\n                type=\"text\"\n                value={answer[blank.id] || \"\"}\n                onChange={(e) => handleBlankChange(blank.id, e.target.value)}\n                disabled={disabled}\n                placeholder={blank.hint || \"...\"}\n                className=\"inline-block w-32 p-1 mx-1 border-b-2 border-primary focus:outline-none focus:border-b-2 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed text-center\"\n              />\n            );\n          }\n          \n          return <span key={index}>{part}</span>;\n        })}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"text-lg leading-relaxed\">\n        {renderTemplate()}\n      </div>\n    </div>\n  );\n};\n\nexport default FillInTheBlankQuestion;\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAcA,MAAM,yBAAgE,CAAC,EACrE,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,cAAc,EACd,SAAS,OAAO,EAChB,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,eAAe;YACb,GAAG,MAAM;YACT,CAAC,QAAQ,EAAE;QACb;IACF;IAEA,gEAAgE;IAChE,MAAM,iBAAiB;QACrB,MAAM,WAAW,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,aAAa,EAAE;QAC1D,MAAM,QAAQ,SAAS,KAAK,CAAC;QAE7B,IAAI,aAAa;QAEjB,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,IAAI,SAAS,WAAW;oBACtB,MAAM,QAAQ,SAAS,MAAM,CAAC,WAAW;oBACzC,MAAM,oBAAoB;oBAC1B;oBAEA,qBACE,6LAAC;wBAEC,MAAK;wBACL,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAC3D,UAAU;wBACV,aAAa,MAAM,IAAI,IAAI;wBAC3B,WAAU;uBANL;;;;;gBASX;gBAEA,qBAAO,6LAAC;8BAAkB;mBAAR;;;;;YACpB;;;;;;IAGN;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;KAvDM;uCAyDS"}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/question-types/EssayQuestion.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { EssayQuestion as EssayQuestionType } from \"@/types/qfjson\";\n\ninterface EssayQuestionProps {\n  question: EssayQuestionType;\n  answer: string | undefined | null;\n  onAnswerChange: (answer: string) => void;\n  disabled?: boolean;\n}\n\nconst EssayQuestion: React.FC<EssayQuestionProps> = ({\n  question,\n  answer = \"\",\n  onAnswerChange,\n  disabled = false,\n}) => {\n  const [wordCount, setWordCount] = useState(0);\n\n  useEffect(() => {\n    // Count words in the answer\n    const answerText = typeof answer === 'string' ? answer : '';\n    const trimmedAnswer = answerText.trim();\n    const words = trimmedAnswer.split(/\\s+/);\n    setWordCount(trimmedAnswer === \"\" ? 0 : words.length);\n  }, [answer]);\n\n  const isUnderMinWordCount =\n    question.min_word_count !== undefined &&\n    wordCount < question.min_word_count;\n\n  const isOverMaxWordCount =\n    question.max_word_count !== undefined &&\n    wordCount > question.max_word_count;\n\n  return (\n    <div className=\"space-y-4\">\n      {question.guidelines && (\n        <div className=\"p-4 bg-muted rounded-md\">\n          <h4 className=\"font-medium mb-2\">Guidelines:</h4>\n          <div className=\"text-sm\">{question.guidelines}</div>\n        </div>\n      )}\n\n      <div className=\"relative\">\n        <textarea\n          value={typeof answer === 'string' ? answer : ''}\n          onChange={(e) => onAnswerChange(e.target.value)}\n          disabled={disabled}\n          placeholder=\"Type your answer here...\"\n          rows={8}\n          className=\"w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary disabled:opacity-60 disabled:cursor-not-allowed\"\n        />\n      </div>\n\n      <div className=\"flex justify-between text-sm\">\n        <div>\n          {(question.min_word_count !== undefined || question.max_word_count !== undefined) && (\n            <span className={\n              isUnderMinWordCount || isOverMaxWordCount\n                ? \"text-destructive\"\n                : \"text-muted-foreground\"\n            }>\n              Word count: {wordCount}\n              {question.min_word_count !== undefined && ` (min: ${question.min_word_count})`}\n              {question.max_word_count !== undefined && ` (max: ${question.max_word_count})`}\n            </span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EssayQuestion;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,EACR,SAAS,EAAE,EACX,cAAc,EACd,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,4BAA4B;YAC5B,MAAM,aAAa,OAAO,WAAW,WAAW,SAAS;YACzD,MAAM,gBAAgB,WAAW,IAAI;YACrC,MAAM,QAAQ,cAAc,KAAK,CAAC;YAClC,aAAa,kBAAkB,KAAK,IAAI,MAAM,MAAM;QACtD;kCAAG;QAAC;KAAO;IAEX,MAAM,sBACJ,SAAS,cAAc,KAAK,aAC5B,YAAY,SAAS,cAAc;IAErC,MAAM,qBACJ,SAAS,cAAc,KAAK,aAC5B,YAAY,SAAS,cAAc;IAErC,qBACE,6LAAC;QAAI,WAAU;;YACZ,SAAS,UAAU,kBAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,6LAAC;wBAAI,WAAU;kCAAW,SAAS,UAAU;;;;;;;;;;;;0BAIjD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,OAAO,OAAO,WAAW,WAAW,SAAS;oBAC7C,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oBAC9C,UAAU;oBACV,aAAY;oBACZ,MAAM;oBACN,WAAU;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BACE,CAAC,SAAS,cAAc,KAAK,aAAa,SAAS,cAAc,KAAK,SAAS,mBAC9E,6LAAC;wBAAK,WACJ,uBAAuB,qBACnB,qBACA;;4BACJ;4BACa;4BACZ,SAAS,cAAc,KAAK,aAAa,CAAC,OAAO,EAAE,SAAS,cAAc,CAAC,CAAC,CAAC;4BAC7E,SAAS,cAAc,KAAK,aAAa,CAAC,OAAO,EAAE,SAAS,cAAc,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAO5F;GA7DM;KAAA;uCA+DS"}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/QuestionRenderer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { Question } from \"@/types/qfjson\";\nimport { getLocalizedText } from \"@/lib/utils/qfjson-parser\";\nimport ReactMarkdown from \"react-markdown\";\n// import { cn } from \"@/lib/utils\";\n\n// Import question type components\nimport MultipleChoiceQuestion from \"./question-types/MultipleChoiceQuestion\";\nimport TrueFalseQuestion from \"./question-types/TrueFalseQuestion\";\nimport ShortAnswerQuestion from \"./question-types/ShortAnswerQuestion\";\nimport MatchingQuestion from \"./question-types/MatchingQuestion\";\nimport FillInTheBlankQuestion from \"./question-types/FillInTheBlankQuestion\";\nimport EssayQuestion from \"./question-types/EssayQuestion\";\n\ninterface QuestionRendererProps {\n  question: Question;\n  answer: any;\n  onAnswerChange: (answer: any) => void;\n  locale?: string;\n  showFeedback?: boolean;\n}\n\nconst QuestionRenderer: React.FC<QuestionRendererProps> = ({\n  question,\n  answer,\n  onAnswerChange,\n  locale = \"en-US\",\n  showFeedback = false,\n}) => {\n  const [visibleHints, setVisibleHints] = useState<string[]>([]);\n\n  // Handle hint visibility based on delay_seconds\n  useEffect(() => {\n    if (!question.hint || question.hint.length === 0) return;\n\n    const timers: NodeJS.Timeout[] = [];\n\n    question.hint.forEach((hint) => {\n      if (hint.delay_seconds && hint.delay_seconds > 0) {\n        const timer = setTimeout(() => {\n          setVisibleHints(prev => [...prev, hint.text]);\n        }, hint.delay_seconds * 1000);\n\n        timers.push(timer);\n      } else {\n        // Show hints with no delay immediately\n        setVisibleHints(prev => [...prev, hint.text]);\n      }\n    });\n\n    return () => {\n      timers.forEach(timer => clearTimeout(timer));\n    };\n  }, [question.hint]);\n\n  // Render question text with markdown support\n  const renderQuestionText = () => {\n    const text = getLocalizedText(question.text, locale);\n    return (\n      <div className=\"prose dark:prose-invert max-w-none\">\n        <ReactMarkdown>\n          {text}\n        </ReactMarkdown>\n      </div>\n    );\n  };\n\n  // Render media items\n  const renderMedia = () => {\n    if (!question.media || question.media.length === 0) return null;\n\n    return (\n      <div className=\"mt-4 space-y-4\">\n        {question.media.map((media, index) => {\n          if (media.type === \"image\") {\n            return (\n              <figure key={index} className=\"relative\">\n                <img\n                  src={media.url}\n                  alt={media.alt_text || \"Question image\"}\n                  className=\"max-w-full rounded-md\"\n                />\n                {media.caption && (\n                  <figcaption className=\"text-sm text-muted-foreground mt-2\">\n                    {media.caption}\n                  </figcaption>\n                )}\n              </figure>\n            );\n          } else if (media.type === \"video\") {\n            return (\n              <figure key={index} className=\"relative\">\n                <video\n                  src={media.url}\n                  controls\n                  className=\"max-w-full rounded-md\"\n                />\n                {media.caption && (\n                  <figcaption className=\"text-sm text-muted-foreground mt-2\">\n                    {media.caption}\n                  </figcaption>\n                )}\n              </figure>\n            );\n          } else if (media.type === \"audio\") {\n            return (\n              <figure key={index} className=\"relative\">\n                <audio src={media.url} controls className=\"w-full\" />\n                {media.caption && (\n                  <figcaption className=\"text-sm text-muted-foreground mt-2\">\n                    {media.caption}\n                  </figcaption>\n                )}\n              </figure>\n            );\n          }\n          return null;\n        })}\n      </div>\n    );\n  };\n\n  // Render hints\n  const renderHints = () => {\n    if (visibleHints.length === 0) return null;\n\n    return (\n      <div className=\"mt-4 p-4 bg-muted rounded-md\">\n        <h4 className=\"font-medium mb-2\">Hints:</h4>\n        <ul className=\"list-disc pl-5 space-y-1\">\n          {visibleHints.map((hint, index) => (\n            <li key={index}>{hint}</li>\n          ))}\n        </ul>\n      </div>\n    );\n  };\n\n  // Render feedback if showFeedback is true\n  const renderFeedback = () => {\n    if (!showFeedback) return null;\n\n    // This is a simplified implementation - in a real app, you would need\n    // to determine if the answer is correct based on question type\n    const isCorrect = false; // Replace with actual logic\n\n    if (isCorrect && question.feedback_correct) {\n      return (\n        <div className=\"mt-4 p-4 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-50 rounded-md\">\n          <ReactMarkdown>{question.feedback_correct}</ReactMarkdown>\n        </div>\n      );\n    } else if (!isCorrect && question.feedback_incorrect) {\n      return (\n        <div className=\"mt-4 p-4 bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-50 rounded-md\">\n          <ReactMarkdown>{question.feedback_incorrect}</ReactMarkdown>\n        </div>\n      );\n    }\n\n    return null;\n  };\n\n  // Render the appropriate question type component\n  const renderQuestionTypeComponent = () => {\n    switch (question.type) {\n      case \"multiple_choice\":\n        return (\n          <MultipleChoiceQuestion\n            question={question as any}\n            answer={answer}\n            onAnswerChange={onAnswerChange}\n            locale={locale}\n          />\n        );\n      case \"true_false\":\n        return (\n          <TrueFalseQuestion\n            question={question as any}\n            answer={answer}\n            onAnswerChange={onAnswerChange}\n          />\n        );\n      case \"short_answer\":\n        return (\n          <ShortAnswerQuestion\n            question={question as any}\n            answer={answer}\n            onAnswerChange={onAnswerChange}\n          />\n        );\n      case \"matching\":\n        return (\n          <MatchingQuestion\n            question={question as any}\n            answer={answer}\n            onAnswerChange={onAnswerChange}\n            locale={locale}\n          />\n        );\n      case \"fill_in_the_blank\":\n        return (\n          <FillInTheBlankQuestion\n            question={question as any}\n            answer={answer}\n            onAnswerChange={onAnswerChange}\n            locale={locale}\n          />\n        );\n      case \"essay\":\n        return (\n          <EssayQuestion\n            question={question as any}\n            answer={answer}\n            onAnswerChange={onAnswerChange}\n          />\n        );\n      default:\n        return (\n          <div className=\"p-4 bg-yellow-50 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-50 rounded-md\">\n            Unsupported question type: {(question as any).type}\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-2\">\n        {renderQuestionText()}\n        {renderMedia()}\n      </div>\n\n      <div className=\"mt-4\">\n        {renderQuestionTypeComponent()}\n      </div>\n\n      {renderHints()}\n      {renderFeedback()}\n    </div>\n  );\n};\n\nexport default QuestionRenderer;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA,oCAAoC;AAEpC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AATA;;;AALA;;;;;;;;;;AAwBA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,MAAM,EACN,cAAc,EACd,SAAS,OAAO,EAChB,eAAe,KAAK,EACrB;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7D,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;YAElD,MAAM,SAA2B,EAAE;YAEnC,SAAS,IAAI,CAAC,OAAO;8CAAC,CAAC;oBACrB,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,GAAG,GAAG;wBAChD,MAAM,QAAQ;gEAAW;gCACvB;wEAAgB,CAAA,OAAQ;+CAAI;4CAAM,KAAK,IAAI;yCAAC;;4BAC9C;+DAAG,KAAK,aAAa,GAAG;wBAExB,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,uCAAuC;wBACvC;0DAAgB,CAAA,OAAQ;uCAAI;oCAAM,KAAK,IAAI;iCAAC;;oBAC9C;gBACF;;YAEA;8CAAO;oBACL,OAAO,OAAO;sDAAC,CAAA,QAAS,aAAa;;gBACvC;;QACF;qCAAG;QAAC,SAAS,IAAI;KAAC;IAElB,6CAA6C;IAC7C,MAAM,qBAAqB;QACzB,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,IAAI,EAAE;QAC7C,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;0BACX;;;;;;;;;;;IAIT;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAAG,OAAO;QAE3D,qBACE,6LAAC;YAAI,WAAU;sBACZ,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO;gBAC1B,IAAI,MAAM,IAAI,KAAK,SAAS;oBAC1B,qBACE,6LAAC;wBAAmB,WAAU;;0CAC5B,6LAAC;gCACC,KAAK,MAAM,GAAG;gCACd,KAAK,MAAM,QAAQ,IAAI;gCACvB,WAAU;;;;;;4BAEX,MAAM,OAAO,kBACZ,6LAAC;gCAAW,WAAU;0CACnB,MAAM,OAAO;;;;;;;uBARP;;;;;gBAajB,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;oBACjC,qBACE,6LAAC;wBAAmB,WAAU;;0CAC5B,6LAAC;gCACC,KAAK,MAAM,GAAG;gCACd,QAAQ;gCACR,WAAU;;;;;;4BAEX,MAAM,OAAO,kBACZ,6LAAC;gCAAW,WAAU;0CACnB,MAAM,OAAO;;;;;;;uBARP;;;;;gBAajB,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;oBACjC,qBACE,6LAAC;wBAAmB,WAAU;;0CAC5B,6LAAC;gCAAM,KAAK,MAAM,GAAG;gCAAE,QAAQ;gCAAC,WAAU;;;;;;4BACzC,MAAM,OAAO,kBACZ,6LAAC;gCAAW,WAAU;0CACnB,MAAM,OAAO;;;;;;;uBAJP;;;;;gBASjB;gBACA,OAAO;YACT;;;;;;IAGN;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI,aAAa,MAAM,KAAK,GAAG,OAAO;QAEtC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAmB;;;;;;8BACjC,6LAAC;oBAAG,WAAU;8BACX,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;sCAAgB;2BAAR;;;;;;;;;;;;;;;;IAKnB;IAEA,0CAA0C;IAC1C,MAAM,iBAAiB;QACrB,IAAI,CAAC,cAAc,OAAO;QAE1B,sEAAsE;QACtE,+DAA+D;QAC/D,MAAM,YAAY,OAAO,4BAA4B;QAErD,uCAA4C;;QAM5C,OAAO,IAAI,CAAC,aAAa,SAAS,kBAAkB,EAAE;YACpD,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;8BAAE,SAAS,kBAAkB;;;;;;;;;;;QAGjD;QAEA,OAAO;IACT;IAEA,iDAAiD;IACjD,MAAM,8BAA8B;QAClC,OAAQ,SAAS,IAAI;YACnB,KAAK;gBACH,qBACE,6LAAC,4KAAA,CAAA,UAAsB;oBACrB,UAAU;oBACV,QAAQ;oBACR,gBAAgB;oBAChB,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,6LAAC,uKAAA,CAAA,UAAiB;oBAChB,UAAU;oBACV,QAAQ;oBACR,gBAAgB;;;;;;YAGtB,KAAK;gBACH,qBACE,6LAAC,yKAAA,CAAA,UAAmB;oBAClB,UAAU;oBACV,QAAQ;oBACR,gBAAgB;;;;;;YAGtB,KAAK;gBACH,qBACE,6LAAC,sKAAA,CAAA,UAAgB;oBACf,UAAU;oBACV,QAAQ;oBACR,gBAAgB;oBAChB,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,6LAAC,4KAAA,CAAA,UAAsB;oBACrB,UAAU;oBACV,QAAQ;oBACR,gBAAgB;oBAChB,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,6LAAC,mKAAA,CAAA,UAAa;oBACZ,UAAU;oBACV,QAAQ;oBACR,gBAAgB;;;;;;YAGtB;gBACE,qBACE,6LAAC;oBAAI,WAAU;;wBAAqF;wBACrE,SAAiB,IAAI;;;;;;;QAG1D;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBACZ;oBACA;;;;;;;0BAGH,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAGF;YACA;;;;;;;AAGP;GA3NM;KAAA;uCA6NS"}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/quiz/QuizRenderer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { QuizFlowJSON, Question } from \"@/types/qfjson\";\nimport { assembleQuizQuestions, getLocalizedText } from \"@/lib/utils/qfjson-parser\";\nimport { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport QuestionRenderer from \"./QuestionRenderer\";\nimport { calculatePercentage } from \"@/lib/utils\";\n\ninterface QuizRendererProps {\n  quiz: QuizFlowJSON;\n  onComplete?: (score: number, answers: Record<string, any>) => void;\n}\n\nconst QuizRenderer: React.FC<QuizRendererProps> = ({ quiz, onComplete }) => {\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [questions, setQuestions] = useState<Question[]>([]);\n  const [answers, setAnswers] = useState<Record<string, any>>({});\n  const [score, setScore] = useState(0);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);\n\n  // Initialize quiz\n  useEffect(() => {\n    const assembledQuestions = assembleQuizQuestions(quiz);\n    setQuestions(assembledQuestions);\n\n    // Initialize time limit if specified\n    if (quiz.quiz.metadata.time_limit_minutes) {\n      setTimeRemaining(quiz.quiz.metadata.time_limit_minutes * 60); // Convert to seconds\n    }\n  }, [quiz]);\n\n  const handleSubmitQuiz = useCallback(() => {\n    // Calculate score\n    let totalScore = 0;\n\n    questions.forEach(question => {\n      // This is a simplified scoring logic - in a real implementation,\n      // you would need more complex logic based on question type\n      const answer = answers[question.question_id];\n      if (answer) {\n        // For true/false questions\n        if (question.type === \"true_false\" && answer === (question as any).correct_answer) {\n          totalScore += question.points;\n        }\n        // For multiple choice with single answer\n        else if (question.type === \"multiple_choice\" &&\n                (question as any).single_correct_answer === true) {\n          const correctOption = (question as any).options.find((opt: any) => opt.is_correct);\n          if (correctOption && answer === correctOption.id) {\n            totalScore += question.points;\n          }\n        }\n        // For short answer questions\n        else if (question.type === \"short_answer\") {\n          const correctAnswers = (question as any).correct_answers;\n          if (correctAnswers.includes(answer)) {\n            totalScore += question.points;\n          }\n        }\n        // Other question types would need their own scoring logic\n      }\n    });\n\n    setScore(totalScore);\n    setIsCompleted(true);\n\n    if (onComplete) {\n      onComplete(totalScore, answers);\n    }\n  }, [questions, answers, onComplete]);\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining === null || timeRemaining <= 0 || isCompleted) return;\n\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => {\n        if (prev === null || prev <= 1) {\n          clearInterval(timer);\n          if (prev === 1) {\n            handleSubmitQuiz();\n          }\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeRemaining, isCompleted, handleSubmitQuiz]);\n\n  const handleAnswerChange = (questionId: string, answer: any) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  const handleNextQuestion = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const handlePreviousQuestion = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n\n\n  const formatTime = (seconds: number): string => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  if (questions.length === 0) {\n    return <div>Loading quiz...</div>;\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const metadata = quiz.quiz.metadata;\n  const locale = metadata.locale || \"en-US\";\n\n  if (isCompleted) {\n    const percentage = calculatePercentage(score, questions.reduce((sum, q) => sum + q.points, 0));\n    const isPassing = metadata.passing_score_percentage\n      ? percentage >= metadata.passing_score_percentage\n      : true;\n\n    return (\n      <Card className=\"w-full max-w-4xl mx-auto\">\n        <CardHeader>\n          <CardTitle>Quiz Results</CardTitle>\n          <CardDescription>\n            {getLocalizedText(metadata.title, locale)}\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"text-center\">\n              <h3 className=\"text-2xl font-bold\">\n                {isPassing ? \"Congratulations!\" : \"Quiz Completed\"}\n              </h3>\n              <p className=\"text-xl mt-2\">\n                Your score: {score} / {questions.reduce((sum, q) => sum + q.points, 0)} ({percentage}%)\n              </p>\n              {metadata.passing_score_percentage && (\n                <p className=\"mt-2\">\n                  {isPassing\n                    ? \"You passed the quiz!\"\n                    : `You need ${metadata.passing_score_percentage}% to pass the quiz.`}\n                </p>\n              )}\n            </div>\n          </div>\n        </CardContent>\n        <CardFooter className=\"justify-center\">\n          <Button onClick={() => window.location.reload()}>\n            Take Another Quiz\n          </Button>\n        </CardFooter>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"w-full max-w-4xl mx-auto\">\n      <CardHeader>\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <CardTitle>{getLocalizedText(metadata.title, locale)}</CardTitle>\n            <CardDescription>\n              Question {currentQuestionIndex + 1} of {questions.length}\n            </CardDescription>\n          </div>\n          {timeRemaining !== null && (\n            <div className=\"text-right\">\n              <p className=\"text-sm text-muted-foreground\">Time Remaining</p>\n              <p className=\"text-xl font-semibold\">{formatTime(timeRemaining)}</p>\n            </div>\n          )}\n        </div>\n      </CardHeader>\n      <CardContent>\n        <QuestionRenderer\n          question={currentQuestion}\n          answer={answers[currentQuestion.question_id]}\n          onAnswerChange={(answer) => handleAnswerChange(currentQuestion.question_id, answer)}\n          locale={locale}\n        />\n      </CardContent>\n      <CardFooter className=\"flex justify-between\">\n        <Button\n          variant=\"outline\"\n          onClick={handlePreviousQuestion}\n          disabled={currentQuestionIndex === 0}\n        >\n          Previous\n        </Button>\n        <div className=\"flex gap-2\">\n          {currentQuestionIndex < questions.length - 1 ? (\n            <Button onClick={handleNextQuestion}>Next</Button>\n          ) : (\n            <Button onClick={handleSubmitQuiz}>Submit Quiz</Button>\n          )}\n        </div>\n      </CardFooter>\n    </Card>\n  );\n};\n\nexport default QuizRenderer;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAeA,MAAM,eAA4C,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE;;IACrE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,qBAAqB,CAAA,GAAA,0IAAA,CAAA,wBAAqB,AAAD,EAAE;YACjD,aAAa;YAEb,qCAAqC;YACrC,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE;gBACzC,iBAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,KAAK,qBAAqB;YACrF;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACnC,kBAAkB;YAClB,IAAI,aAAa;YAEjB,UAAU,OAAO;8DAAC,CAAA;oBAChB,iEAAiE;oBACjE,2DAA2D;oBAC3D,MAAM,SAAS,OAAO,CAAC,SAAS,WAAW,CAAC;oBAC5C,IAAI,QAAQ;wBACV,2BAA2B;wBAC3B,IAAI,SAAS,IAAI,KAAK,gBAAgB,WAAW,AAAC,SAAiB,cAAc,EAAE;4BACjF,cAAc,SAAS,MAAM;wBAC/B,OAEK,IAAI,SAAS,IAAI,KAAK,qBACnB,AAAC,SAAiB,qBAAqB,KAAK,MAAM;4BACxD,MAAM,gBAAgB,AAAC,SAAiB,OAAO,CAAC,IAAI;4FAAC,CAAC,MAAa,IAAI,UAAU;;4BACjF,IAAI,iBAAiB,WAAW,cAAc,EAAE,EAAE;gCAChD,cAAc,SAAS,MAAM;4BAC/B;wBACF,OAEK,IAAI,SAAS,IAAI,KAAK,gBAAgB;4BACzC,MAAM,iBAAiB,AAAC,SAAiB,eAAe;4BACxD,IAAI,eAAe,QAAQ,CAAC,SAAS;gCACnC,cAAc,SAAS,MAAM;4BAC/B;wBACF;oBACA,0DAA0D;oBAC5D;gBACF;;YAEA,SAAS;YACT,eAAe;YAEf,IAAI,YAAY;gBACd,WAAW,YAAY;YACzB;QACF;qDAAG;QAAC;QAAW;QAAS;KAAW;IAEnC,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB,QAAQ,iBAAiB,KAAK,aAAa;YAEjE,MAAM,QAAQ;gDAAY;oBACxB;wDAAiB,CAAA;4BACf,IAAI,SAAS,QAAQ,QAAQ,GAAG;gCAC9B,cAAc;gCACd,IAAI,SAAS,GAAG;oCACd;gCACF;gCACA,OAAO;4BACT;4BACA,OAAO,OAAO;wBAChB;;gBACF;+CAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;QAAe;QAAa;KAAiB;IAEjD,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,IAAI,uBAAuB,UAAU,MAAM,GAAG,GAAG;YAC/C,wBAAwB,CAAA,OAAQ,OAAO;QACzC;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,uBAAuB,GAAG;YAC5B,wBAAwB,CAAA,OAAQ,OAAO;QACzC;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACrE;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBAAO,6LAAC;sBAAI;;;;;;IACd;IAEA,MAAM,kBAAkB,SAAS,CAAC,qBAAqB;IACvD,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;IACnC,MAAM,SAAS,SAAS,MAAM,IAAI;IAElC,IAAI,aAAa;QACf,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAC3F,MAAM,YAAY,SAAS,wBAAwB,GAC/C,cAAc,SAAS,wBAAwB,GAC/C;QAEJ,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,6LAAC,mIAAA,CAAA,kBAAe;sCACb,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,KAAK,EAAE;;;;;;;;;;;;8BAGtC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,YAAY,qBAAqB;;;;;;8CAEpC,6LAAC;oCAAE,WAAU;;wCAAe;wCACb;wCAAM;wCAAI,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;wCAAG;wCAAG;wCAAW;;;;;;;gCAEtF,SAAS,wBAAwB,kBAChC,6LAAC;oCAAE,WAAU;8CACV,YACG,yBACA,CAAC,SAAS,EAAE,SAAS,wBAAwB,CAAC,mBAAmB,CAAC;;;;;;;;;;;;;;;;;;;;;;8BAMhF,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kCAAI;;;;;;;;;;;;;;;;;IAMzD;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,YAAS;8CAAE,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,KAAK,EAAE;;;;;;8CAC7C,6LAAC,mIAAA,CAAA,kBAAe;;wCAAC;wCACL,uBAAuB;wCAAE;wCAAK,UAAU,MAAM;;;;;;;;;;;;;wBAG3D,kBAAkB,sBACjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAC7C,6LAAC;oCAAE,WAAU;8CAAyB,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAKzD,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,iJAAA,CAAA,UAAgB;oBACf,UAAU;oBACV,QAAQ,OAAO,CAAC,gBAAgB,WAAW,CAAC;oBAC5C,gBAAgB,CAAC,SAAW,mBAAmB,gBAAgB,WAAW,EAAE;oBAC5E,QAAQ;;;;;;;;;;;0BAGZ,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,yBAAyB;kCACpC;;;;;;kCAGD,6LAAC;wBAAI,WAAU;kCACZ,uBAAuB,UAAU,MAAM,GAAG,kBACzC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAoB;;;;;iDAErC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAkB;;;;;;;;;;;;;;;;;;;;;;;AAM/C;GAxMM;KAAA;uCA0MS"}}, {"offset": {"line": 1782, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}