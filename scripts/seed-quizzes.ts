#!/usr/bin/env tsx

/**
 * Quiz Seeding Script
 *
 * This script seeds the database with hacking-related quizzes following the QFJSON specification.
 * It imports quiz data from JSON files and creates corresponding database entries.
 */

import { PrismaClient } from '@/generated/prisma';
import { readFileSync } from 'fs';
import { join } from 'path';
import { QuizFlowJSON } from '@/types/qfjson';
import { getLocalizedText } from '@/lib/utils/qfjson-parser';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

// Quiz file paths
const quizFiles = [
  'src/data/sample-quiz.json',
  'src/data/web-app-security-quiz.json',
  'src/data/network-security-quiz.json',
  'src/data/social-engineering-quiz.json',
  'src/data/cryptography-forensics-quiz.json',
  'src/data/malware-analysis-quiz.json',
  'src/data/enhanced-hints-quiz.json',
];

async function seedQuizzes() {
  console.log('🌱 Starting quiz seeding process...');

  try {
    // Create a default admin user if it doesn't exist
    let adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('👤 Creating admin user...');
      try {
        // Hash the admin password
        const hashedPassword = await bcrypt.hash('admin123', 10);

        adminUser = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: 'QuizFlow Admin',
            role: 'admin',
            password: hashedPassword
          }
        });
        console.log('✅ Admin user created with email: <EMAIL> and password: admin123');
      } catch (error) {
        console.log('⚠️  Admin user creation failed, trying to find existing user...');
        adminUser = await prisma.user.findUnique({
          where: { email: '<EMAIL>' }
        });
        if (!adminUser) {
          throw new Error('Could not create or find admin user');
        }
      }
    } else {
      console.log('✅ Admin user already exists: <EMAIL>');
    }

    // Process each quiz file
    for (const filePath of quizFiles) {
      console.log(`📚 Processing ${filePath}...`);

      try {
        // Read and parse the quiz file
        const fullPath = join(process.cwd(), filePath);
        const fileContent = readFileSync(fullPath, 'utf-8');
        const quizData: QuizFlowJSON = JSON.parse(fileContent);

        const metadata = quizData.quiz.metadata;

        // Check if quiz already exists
        const existingQuiz = await prisma.quiz.findUnique({
          where: { quizId: metadata.quiz_id }
        });

        if (existingQuiz) {
          console.log(`⚠️  Quiz ${metadata.quiz_id} already exists, skipping...`);
          continue;
        }

        // Create the quiz
        const quiz = await prisma.quiz.create({
          data: {
            quizId: metadata.quiz_id,
            title: getLocalizedText(metadata.title, metadata.locale),
            description: getLocalizedText(metadata.description, metadata.locale) || '',
            author: metadata.author || 'QuizFlow Team',
            tags: metadata.tags || [],
            passingScore: metadata.passing_score_percentage || 70,
            timeLimit: metadata.time_limit_minutes || 30,
            markupFormat: metadata.markup_format || 'markdown',
            locale: metadata.locale || 'en-US',
            formatVersion: metadata.format_version,
            isPublished: true,
            creatorId: adminUser.id,
          }
        });

        console.log(`✅ Created quiz: ${quiz.title}`);

        // Create questions
        if (quizData.quiz.questions) {
          for (const questionData of quizData.quiz.questions) {
            await prisma.question.create({
              data: {
                questionId: questionData.question_id,
                type: questionData.type,
                text: questionData.text,
                points: questionData.points,
                feedbackCorrect: questionData.feedback_correct,
                feedbackIncorrect: questionData.feedback_incorrect,
                explanation: questionData.explanation || null,
                media: questionData.media || null,
                hint: questionData.hint || null,
                dependsOn: questionData.depends_on || null,

                // Type-specific fields stored as JSON
                options: (questionData as any).options || null,
                correctAnswer: (questionData as any).correct_answer || null,
                correctAnswers: (questionData as any).correct_answers || null,
                caseSensitive: (questionData as any).case_sensitive || null,
                trimWhitespace: (questionData as any).trim_whitespace || null,
                exactMatch: (questionData as any).exact_match || null,
                stems: (questionData as any).stems || null,
                correctPairs: (questionData as any).correct_pairs || null,
                textTemplate: (questionData as any).text_template || null,
                blanks: (questionData as any).blanks || null,
                minWordCount: (questionData as any).min_word_count || null,
                maxWordCount: (questionData as any).max_word_count || null,
                guidelines: (questionData as any).guidelines || null,

                quizId: quiz.id,
              }
            });
          }
          console.log(`✅ Created ${quizData.quiz.questions.length} questions for ${quiz.title}`);
        }

        // Create question pools if they exist
        if (quizData.quiz.question_pools) {
          for (const poolData of quizData.quiz.question_pools) {
            const pool = await prisma.questionPool.create({
              data: {
                poolId: poolData.pool_id,
                title: poolData.title,
                description: poolData.description,
                quizId: quiz.id,
              }
            });

            // Create questions for this pool
            for (const questionData of poolData.questions) {
              await prisma.question.create({
                data: {
                  questionId: questionData.question_id,
                  type: questionData.type,
                  text: questionData.text,
                  points: questionData.points,
                  feedbackCorrect: questionData.feedback_correct,
                  feedbackIncorrect: questionData.feedback_incorrect,
                  explanation: questionData.explanation || null,
                  media: questionData.media || null,
                  hint: questionData.hint || null,
                  dependsOn: questionData.depends_on || null,

                  // Type-specific fields
                  options: (questionData as any).options || null,
                  correctAnswer: (questionData as any).correct_answer || null,
                  correctAnswers: (questionData as any).correct_answers || null,
                  caseSensitive: (questionData as any).case_sensitive || null,
                  trimWhitespace: (questionData as any).trim_whitespace || null,
                  exactMatch: (questionData as any).exact_match || null,
                  stems: (questionData as any).stems || null,
                  correctPairs: (questionData as any).correct_pairs || null,
                  textTemplate: (questionData as any).text_template || null,
                  blanks: (questionData as any).blanks || null,
                  minWordCount: (questionData as any).min_word_count || null,
                  maxWordCount: (questionData as any).max_word_count || null,
                  guidelines: (questionData as any).guidelines || null,

                  quizId: quiz.id,
                  questionPoolId: pool.id,
                }
              });
            }
          }
        }

        // Create selection rules if they exist
        if (quizData.quiz.selection_rules) {
          for (const ruleData of quizData.quiz.selection_rules) {
            await prisma.selectionRule.create({
              data: {
                poolId: ruleData.pool_id,
                selectCount: ruleData.select_count,
                randomize: ruleData.randomize || false,
                shuffleOrder: ruleData.shuffle_order || false,
                quizId: quiz.id,
              }
            });
          }
        }

      } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error);
      }
    }

    console.log('🎉 Quiz seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error during seeding:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedQuizzes();
}

export default seedQuizzes;
