#!/usr/bin/env tsx

/**
 * Admin Setup Script
 * 
 * This script creates or updates the admin user with proper credentials.
 */

import { PrismaClient } from '@/generated/prisma';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function setupAdmin() {
  console.log('🔧 Setting up admin user...');

  try {
    // Hash the admin password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Try to find existing admin user
    let adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (adminUser) {
      // Update existing admin user
      console.log('👤 Updating existing admin user...');
      adminUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          name: 'QuizFlow Admin',
          role: 'admin',
          password: hashedPassword
        }
      });
      console.log('✅ Admin user updated successfully!');
    } else {
      // Create new admin user
      console.log('👤 Creating new admin user...');
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'QuizFlow Admin',
          role: 'admin',
          password: hashedPassword
        }
      });
      console.log('✅ Admin user created successfully!');
    }

    console.log('\n📋 Admin Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('   Role: admin');
    console.log('\n🚀 You can now log in to the dashboard!');
    
  } catch (error) {
    console.error('❌ Error setting up admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the setup function
if (require.main === module) {
  setupAdmin();
}

export default setupAdmin;
