#!/usr/bin/env tsx

/**
 * Check Admin Script
 * 
 * This script checks if the admin user exists and verifies the password hash.
 */

import { PrismaClient } from '@/generated/prisma';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function checkAdmin() {
  console.log('🔍 Checking admin user...');

  try {
    // Find the admin user
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found!');
      return;
    }

    console.log('✅ Admin user found:');
    console.log('   ID:', adminUser.id);
    console.log('   Name:', adminUser.name);
    console.log('   Email:', adminUser.email);
    console.log('   Role:', adminUser.role);
    console.log('   Created:', adminUser.createdAt);

    // Test password verification
    if (adminUser.password) {
      const isPasswordValid = await bcrypt.compare('admin123', adminUser.password);
      console.log('   Password Hash Valid:', isPasswordValid ? '✅' : '❌');
      console.log('   Password Hash:', adminUser.password.substring(0, 20) + '...');
    } else {
      console.log('   Password: ❌ No password set');
    }
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check function
if (require.main === module) {
  checkAdmin();
}

export default checkAdmin;
