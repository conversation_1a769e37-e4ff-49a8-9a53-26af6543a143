#!/usr/bin/env tsx

/**
 * Test Login Script
 * 
 * This script tests the login functionality manually.
 */

import { PrismaClient } from '@/generated/prisma';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function testLogin() {
  console.log('🧪 Testing login functionality...');

  try {
    const email = '<EMAIL>';
    const password = 'admin123';

    console.log(`\n1. Looking for user with email: ${email}`);
    
    const user = await prisma.user.findUnique({
      where: {
        email: email,
      },
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:', {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      hasPassword: !!user.password
    });

    if (!user.password) {
      console.log('❌ User has no password set');
      return;
    }

    console.log(`\n2. Testing password: ${password}`);
    
    const passwordMatch = await bcrypt.compare(password, user.password);
    
    if (!passwordMatch) {
      console.log('❌ Password does not match');
      return;
    }

    console.log('✅ Password matches!');

    console.log('\n3. User object that would be returned:');
    const userResult = {
      id: user.id,
      name: user.name,
      email: user.email,
      image: user.image,
      role: user.role,
    };
    console.log(userResult);

    console.log('\n🎉 Login test successful! The credentials should work.');
    
  } catch (error) {
    console.error('❌ Error during login test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test function
if (require.main === module) {
  testLogin();
}

export default testLogin;
